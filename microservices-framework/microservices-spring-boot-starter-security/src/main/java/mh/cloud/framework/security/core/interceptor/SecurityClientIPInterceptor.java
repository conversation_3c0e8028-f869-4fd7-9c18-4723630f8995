package mh.cloud.framework.security.core.interceptor;

import cn.hutool.core.util.StrUtil;
import mh.cloud.framework.common.interceptor.ClientIPInterceptor;
import mh.cloud.framework.common.util.servlet.ClientIPUtils;
import mh.cloud.framework.security.core.LoginUser;
import mh.cloud.framework.security.core.util.SecurityFrameworkUtils;
import lombok.extern.slf4j.Slf4j;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 安全框架集成的客户端IP记录拦截器
 * 继承基础拦截器，增加Security框架集成功能
 */
@Slf4j
public class SecurityClientIPInterceptor extends ClientIPInterceptor {
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String requestURI = request.getRequestURI();
        
        // 跳过不需要记录的请求
        if (shouldSkipLogging(requestURI)) {
            return true;
        }
        
        try {
            // 获取客户端IP信息
            ClientIPUtils.ClientIPInfo ipInfo = ClientIPUtils.getClientIPInfo(request);
            
            // 从Security框架获取用户信息
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            String userId = loginUser != null ? loginUser.getId() : "anonymous";
            String userType = loginUser != null ? String.valueOf(loginUser.getUserType()) : "unknown";
            
            // 判断是否需要详细记录
            if (shouldDetailedLog(requestURI)) {
                logDetailedClientInfo(ipInfo, userId, userType, requestURI, loginUser);
            } else {
                logBasicClientInfo(ipInfo, userId, requestURI);
            }
            
            // 将IP信息和用户信息设置到请求属性中
            request.setAttribute("CLIENT_IP_INFO", ipInfo);
            request.setAttribute("CLIENT_IP", ipInfo.getIp());
            request.setAttribute("LOGIN_USER_INFO", loginUser);
            
        } catch (Exception e) {
            log.warn("Failed to record client IP for request: {}, error: {}", requestURI, e.getMessage());
        }
        
        return true;
    }
    
    /**
     * 记录详细的客户端信息（包含Security信息）
     */
    private void logDetailedClientInfo(ClientIPUtils.ClientIPInfo ipInfo, String userId, String userType, 
                                     String requestURI, LoginUser loginUser) {
        log.info("=== 客户端访问详细信息（Security增强） ===");
        log.info("请求URI: {}", requestURI);
        log.info("用户ID: {}", userId);
        log.info("用户类型: {}", userType);
        log.info("客户端IP: {}", ipInfo.getIp());
        log.info("IP来源: {}", ipInfo.getSource());
        log.info("原始值: {}", ipInfo.getRawValue());
        log.info("User-Agent: {}", ipInfo.getUserAgent());
        log.info("请求方法: {}", ipInfo.getRequestMethod());
        
        if (loginUser != null) {
            log.info("租户ID: {}", loginUser.getTenantId());
            log.info("权限范围: {}", loginUser.getScopes());
        }
        
        log.info("==========================================");
    }
    
    /**
     * 记录基本的客户端信息
     */
    private void logBasicClientInfo(ClientIPUtils.ClientIPInfo ipInfo, String userId, String requestURI) {
        log.debug("Security Client Access - User: {}, IP: {}, URI: {} {}", 
                userId, ipInfo.getIp(), ipInfo.getRequestMethod(), requestURI);
    }
    
    /**
     * 判断是否应该跳过日志记录
     */
    private boolean shouldSkipLogging(String requestURI) {
        if (StrUtil.isBlank(requestURI)) {
            return true;
        }
        
        String[] skipPatterns = {
            "/admin-api/infra/file/",
            "/favicon.ico",
            "/actuator/",
            "/swagger-ui/",
            "/v3/api-docs"
        };
        
        for (String pattern : skipPatterns) {
            if (requestURI.contains(pattern)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 判断是否需要详细记录
     */
    private boolean shouldDetailedLog(String requestURI) {
        if (StrUtil.isBlank(requestURI)) {
            return false;
        }
        
        String[] detailedPatterns = {
            "/admin-api/system/auth/",
            "/app-api/member/auth/",
            "/admin-api/system/oauth2/",
            "/admin-api/system/captcha/"
        };
        
        for (String pattern : detailedPatterns) {
            if (requestURI.contains(pattern)) {
                return true;
            }
        }
        
        return false;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 调用父类清理方法
        super.afterCompletion(request, response, handler, ex);
        
        // 清理Security相关属性
        request.removeAttribute("LOGIN_USER_INFO");
    }
}
