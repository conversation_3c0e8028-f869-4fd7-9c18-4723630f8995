package mh.cloud.framework.security.config;

import mh.cloud.framework.security.core.interceptor.SecurityClientIPInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * Security客户端IP记录自动配置
 * 当Security框架存在时，提供增强的IP记录功能
 */
@AutoConfiguration
@ConditionalOnClass(name = "org.springframework.security.core.Authentication")
@Slf4j
public class SecurityClientIPAutoConfiguration {
    
    /**
     * Security集成的客户端IP拦截器
     */
    @Bean("securityClientIPInterceptor")
    @ConditionalOnProperty(name = "microservices.web.client-ip.enable", havingValue = "true", matchIfMissing = true)
    public HandlerInterceptor securityClientIPInterceptor() {
        log.info("注册Security集成的客户端IP拦截器");
        return new SecurityClientIPInterceptor();
    }
}
