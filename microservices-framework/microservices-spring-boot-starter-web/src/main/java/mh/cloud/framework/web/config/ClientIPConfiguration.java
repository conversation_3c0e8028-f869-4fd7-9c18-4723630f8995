package mh.cloud.framework.web.config;

import mh.cloud.framework.common.interceptor.ClientIPInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 客户端IP记录配置类
 * 根据环境条件选择合适的拦截器实现
 */
@Configuration
@Slf4j
public class ClientIPConfiguration {
    
    /**
     * 基础客户端IP拦截器
     * 当Security框架不存在时使用
     */
    @Bean
    @ConditionalOnMissingClass("mh.cloud.framework.security.core.util.SecurityFrameworkUtils")
    @ConditionalOnProperty(name = "microservices.web.client-ip.enable", havingValue = "true", matchIfMissing = true)
    public HandlerInterceptor basicClientIPInterceptor() {
        log.info("注册基础客户端IP拦截器");
        return new ClientIPInterceptor();
    }
    
    /**
     * Security集成的客户端IP拦截器
     * 当Security框架存在时使用
     */
    @Bean
    @ConditionalOnClass(name = "mh.cloud.framework.security.core.util.SecurityFrameworkUtils")
    @ConditionalOnProperty(name = "microservices.web.client-ip.enable", havingValue = "true", matchIfMissing = true)
    public HandlerInterceptor securityClientIPInterceptor() {
        try {
            log.info("检测到Security框架，尝试注册Security集成的客户端IP拦截器");
            // 使用反射创建Security集成的拦截器，避免直接依赖
            Class<?> interceptorClass = Class.forName("mh.cloud.framework.security.core.interceptor.SecurityClientIPInterceptor");
            Object interceptor = interceptorClass.getDeclaredConstructor().newInstance();
            log.info("成功创建Security集成的客户端IP拦截器");
            return (HandlerInterceptor) interceptor;
        } catch (ClassNotFoundException e) {
            log.info("Security集成拦截器类不存在，使用基础拦截器");
            return new ClientIPInterceptor();
        } catch (Exception e) {
            log.warn("无法创建Security集成拦截器，回退到基础拦截器: {}", e.getMessage());
            return new ClientIPInterceptor();
        }
    }
}
