package mh.cloud.framework.web.core.interceptor;

import cn.hutool.core.util.StrUtil;
import mh.cloud.framework.common.util.servlet.ClientIPUtils;
import mh.cloud.framework.common.util.servlet.ServletUtils;
import mh.cloud.framework.security.core.LoginUser;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.security.core.util.SecurityFrameworkUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 客户端IP记录拦截器
 * 在每个请求处理前记录客户端IP信息
 */
@Slf4j
public class ClientIPInterceptor implements HandlerInterceptor {
    
    /**
     * 需要记录详细IP信息的URI模式
     */
    private static final String[] DETAILED_LOG_PATTERNS = {
        "/admin-api/system/auth/",
        "/app-api/member/auth/",
        "/admin-api/system/oauth2/",
        "/admin-api/system/captcha/"
    };
    
    /**
     * 需要跳过记录的URI模式
     */
    private static final String[] SKIP_LOG_PATTERNS = {
        "/admin-api/infra/file/",
        "/favicon.ico",
        "/actuator/",
        "/swagger-ui/",
        "/v3/api-docs"
    };
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String requestURI = request.getRequestURI();
        
        // 跳过不需要记录的请求
        if (shouldSkipLogging(requestURI)) {
            return true;
        }
        
        try {
            // 获取客户端IP信息
            ClientIPUtils.ClientIPInfo ipInfo = ClientIPUtils.getClientIPInfo(request);
            
            // 获取当前登录用户信息
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            String userId = loginUser != null ? loginUser.getId() : "anonymous";
            String userType = loginUser != null ? String.valueOf(loginUser.getUserType()) : "unknown";
            
            // 判断是否需要详细记录
            if (shouldDetailedLog(requestURI)) {
                logDetailedClientInfo(ipInfo, userId, userType, requestURI);
            } else {
                logBasicClientInfo(ipInfo, userId, requestURI);
            }
            
            // 将IP信息设置到请求属性中，供后续使用
            request.setAttribute("CLIENT_IP_INFO", ipInfo);
            request.setAttribute("CLIENT_IP", ipInfo.getIp());
            
        } catch (Exception e) {
            log.warn("Failed to record client IP for request: {}, error: {}", requestURI, e.getMessage());
        }
        
        return true;
    }
    
    /**
     * 记录详细的客户端信息
     */
    private void logDetailedClientInfo(ClientIPUtils.ClientIPInfo ipInfo, String userId, String userType, String requestURI) {
        log.info("=== 客户端访问详细信息 ===");
        log.info("请求URI: {}", requestURI);
        log.info("用户ID: {}", userId);
        log.info("用户类型: {}", userType);
        log.info("客户端IP: {}", ipInfo.getIp());
        log.info("IP来源: {}", ipInfo.getSource());
        log.info("原始值: {}", ipInfo.getRawValue());
        log.info("User-Agent: {}", ipInfo.getUserAgent());
        log.info("请求方法: {}", ipInfo.getRequestMethod());
        log.info("========================");
    }
    
    /**
     * 记录基本的客户端信息
     */
    private void logBasicClientInfo(ClientIPUtils.ClientIPInfo ipInfo, String userId, String requestURI) {
        log.debug("Client Access - User: {}, IP: {}, URI: {} {}", 
                userId, ipInfo.getIp(), ipInfo.getRequestMethod(), requestURI);
    }
    
    /**
     * 判断是否应该跳过日志记录
     */
    private boolean shouldSkipLogging(String requestURI) {
        if (StrUtil.isBlank(requestURI)) {
            return true;
        }
        
        for (String pattern : SKIP_LOG_PATTERNS) {
            if (requestURI.contains(pattern)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 判断是否需要详细记录
     */
    private boolean shouldDetailedLog(String requestURI) {
        if (StrUtil.isBlank(requestURI)) {
            return false;
        }
        
        for (String pattern : DETAILED_LOG_PATTERNS) {
            if (requestURI.contains(pattern)) {
                return true;
            }
        }
        
        return false;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 清理请求属性
        request.removeAttribute("CLIENT_IP_INFO");
        request.removeAttribute("CLIENT_IP");
    }
}
