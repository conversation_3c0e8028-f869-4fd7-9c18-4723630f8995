package mh.cloud.framework.common.util.servlet;

import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import jakarta.servlet.http.HttpServletRequest;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.List;

/**
 * 客户端IP获取工具类
 * 增强版本，支持更多的代理头和更准确的IP识别
 */
@Slf4j
public class ClientIPUtils {
    
    /**
     * 常见的代理头列表，按优先级排序
     */
    private static final List<String> PROXY_HEADERS = Arrays.asList(
        "X-Forwarded-For",
        "X-Real-IP", 
        "X-Original-Forwarded-For",
        "Proxy-Client-IP",
        "WL-Proxy-Client-IP",
        "HTTP_X_FORWARDED_FOR",
        "HTTP_X_FORWARDED",
        "HTTP_X_CLUSTER_CLIENT_IP",
        "HTTP_CLIENT_IP",
        "HTTP_FORWARDED_FOR",
        "HTTP_FORWARDED",
        "HTTP_VIA",
        "REMOTE_ADDR"
    );
    
    /**
     * 获取客户端真实IP地址
     * 
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    public static String getClientIP(HttpServletRequest request) {
        if (request == null) {
            log.warn("HttpServletRequest is null, cannot get client IP");
            return "unknown";
        }
        
        String ip = null;
        
        // 1. 尝试从各种代理头中获取IP
        for (String header : PROXY_HEADERS) {
            ip = request.getHeader(header);
            if (isValidIP(ip)) {
                // 如果是多级代理，取第一个IP
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                if (isValidIP(ip)) {
                    log.debug("Got client IP from header {}: {}", header, ip);
                    return normalizeIP(ip);
                }
            }
        }
        
        // 2. 从RemoteAddr获取
        ip = request.getRemoteAddr();
        if (isValidIP(ip)) {
            log.debug("Got client IP from RemoteAddr: {}", ip);
            return normalizeIP(ip);
        }
        
        // 3. 如果都获取不到，返回默认值
        log.warn("Cannot get valid client IP, using default: 127.0.0.1");
        return "127.0.0.1";
    }
    
    /**
     * 获取客户端IP的详细信息
     * 
     * @param request HTTP请求对象
     * @return IP详细信息
     */
    public static ClientIPInfo getClientIPInfo(HttpServletRequest request) {
        ClientIPInfo info = new ClientIPInfo();
        
        if (request == null) {
            info.setIp("unknown");
            info.setSource("request_null");
            return info;
        }
        
        // 尝试从各种头中获取IP
        for (String header : PROXY_HEADERS) {
            String ip = request.getHeader(header);
            if (isValidIP(ip)) {
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                if (isValidIP(ip)) {
                    info.setIp(normalizeIP(ip));
                    info.setSource(header);
                    info.setRawValue(request.getHeader(header));
                    break;
                }
            }
        }
        
        // 如果没有从头中获取到，尝试RemoteAddr
        if (info.getIp() == null) {
            String ip = request.getRemoteAddr();
            if (isValidIP(ip)) {
                info.setIp(normalizeIP(ip));
                info.setSource("RemoteAddr");
                info.setRawValue(ip);
            } else {
                info.setIp("127.0.0.1");
                info.setSource("default");
                info.setRawValue("unknown");
            }
        }
        
        // 设置其他信息
        info.setUserAgent(request.getHeader("User-Agent"));
        info.setRequestURI(request.getRequestURI());
        info.setRequestMethod(request.getMethod());
        
        return info;
    }
    
    /**
     * 验证IP地址是否有效
     * 
     * @param ip IP地址
     * @return 是否有效
     */
    private static boolean isValidIP(String ip) {
        if (StrUtil.isBlank(ip)) {
            return false;
        }
        
        // 过滤无效值
        String[] invalidValues = {"unknown", "null", "undefined", "-", "0.0.0.0"};
        for (String invalid : invalidValues) {
            if (invalid.equalsIgnoreCase(ip.trim())) {
                return false;
            }
        }

        // 使用hutool验证IP格式
        return NetUtil.isInnerIP(ip.trim());
    }
    
    /**
     * 标准化IP地址
     * 
     * @param ip 原始IP
     * @return 标准化后的IP
     */
    private static String normalizeIP(String ip) {
        if (StrUtil.isBlank(ip)) {
            return "127.0.0.1";
        }
        
        try {
            InetAddress inet = InetAddress.getByName(ip.trim());
            
            // 处理环回地址
            if (inet.isLoopbackAddress()) {
                return "127.0.0.1";
            }
            
            // 处理IPv6映射的IPv4地址
            if (inet instanceof java.net.Inet6Address && ip.startsWith("::ffff:")) {
                return ip.substring(7);
            }
            
            return ip.trim();
            
        } catch (UnknownHostException e) {
            log.warn("Invalid IP address: {}, using default", ip);
            return "127.0.0.1";
        }
    }
    
    /**
     * 记录客户端访问信息到日志
     * 
     * @param request HTTP请求
     * @param action 操作描述
     */
    public static void logClientAccess(HttpServletRequest request, String action) {
        ClientIPInfo info = getClientIPInfo(request);
        log.info("Client Access - Action: {}, IP: {}, Source: {}, UserAgent: {}, URI: {} {}", 
                action, info.getIp(), info.getSource(), info.getUserAgent(), 
                info.getRequestMethod(), info.getRequestURI());
    }
    
    /**
     * 客户端IP详细信息类
     */
    public static class ClientIPInfo {
        private String ip;
        private String source;
        private String rawValue;
        private String userAgent;
        private String requestURI;
        private String requestMethod;
        
        // Getters and Setters
        public String getIp() { return ip; }
        public void setIp(String ip) { this.ip = ip; }
        
        public String getSource() { return source; }
        public void setSource(String source) { this.source = source; }
        
        public String getRawValue() { return rawValue; }
        public void setRawValue(String rawValue) { this.rawValue = rawValue; }
        
        public String getUserAgent() { return userAgent; }
        public void setUserAgent(String userAgent) { this.userAgent = userAgent; }
        
        public String getRequestURI() { return requestURI; }
        public void setRequestURI(String requestURI) { this.requestURI = requestURI; }
        
        public String getRequestMethod() { return requestMethod; }
        public void setRequestMethod(String requestMethod) { this.requestMethod = requestMethod; }
        
        @Override
        public String toString() {
            return String.format("ClientIPInfo{ip='%s', source='%s', rawValue='%s', userAgent='%s', uri='%s %s'}", 
                    ip, source, rawValue, userAgent, requestMethod, requestURI);
        }
    }
}
