package mh.cloud.framework.common.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@ConfigurationProperties(prefix = "microservices.web")
@Validated
@Data
public class WebProperties {

    @NotNull(message = "APP API 不能为空")
    private Api appApi = new Api("/app-api", "**.controller.app.**");
    @NotNull(message = "Admin API 不能为空")
    private Api adminApi = new Api("/admin-api", "**.controller.admin.**");

    @NotNull(message = "Admin UI 不能为空")
    private Ui adminUi;

    /**
     * 客户端IP记录配置
     */
    private ClientIP clientIp = new ClientIP();

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Valid
    public static class Api {

        /**
         * API 前缀，实现所有 Controller 提供的 RESTFul API 的统一前缀
         *
         *
         * 意义：通过该前缀，避免 Swagger、Actuator 意外通过 Nginx 暴露出来给外部，带来安全性问题
         *      这样，Nginx 只需要配置转发到 /api/* 的所有接口即可。
         */
        @NotEmpty(message = "API 前缀不能为空")
        private String prefix;

        /**
         * Controller 所在包的 Ant 路径规则
         *
         * 主要目的是，给该 Controller 设置指定的 {@link #prefix}
         */
        @NotEmpty(message = "Controller 所在包不能为空")
        private String controller;

    }

    @Data
    @Valid
    public static class Ui {

        /**
         * 访问地址
         */
        private String url;

    }

    /**
     * 客户端IP记录配置
     */
    @Data
    public static class ClientIP {

        /**
         * 是否开启客户端IP记录
         */
        private Boolean enable = true;

        /**
         * 是否记录详细信息
         */
        private Boolean detailed = false;

        /**
         * 需要详细记录的URI模式
         */
        private String[] detailedPatterns = {
            "/admin-api/system/auth/",
            "/app-api/member/auth/",
            "/admin-api/system/oauth2/"
        };

        /**
         * 需要跳过记录的URI模式
         */
        private String[] skipPatterns = {
            "/admin-api/infra/file/",
            "/favicon.ico",
            "/actuator/",
            "/swagger-ui/",
            "/v3/api-docs"
        };
    }

}
