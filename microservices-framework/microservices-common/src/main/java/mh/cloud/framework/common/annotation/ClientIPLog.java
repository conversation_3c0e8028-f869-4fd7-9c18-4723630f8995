package mh.cloud.framework.common.annotation;

import java.lang.annotation.*;

/**
 * 客户端IP记录注解
 * 用于标记需要记录客户端IP的方法或类
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ClientIPLog {
    
    /**
     * 操作描述
     */
    String value() default "";
    
    /**
     * 是否记录详细信息
     */
    boolean detailed() default false;
    
    /**
     * 是否启用（可用于动态控制）
     */
    boolean enable() default true;
}
