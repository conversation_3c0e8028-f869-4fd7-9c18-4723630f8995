package mh.cloud.framework.common.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import mh.cloud.framework.common.exception.ErrorCode;
import mh.cloud.framework.common.exception.ServiceException;
import mh.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.Objects;

/**
 * @Author: ytq
 * @Date: 2025/3/27 14:05
 * @Description: 外部登录返回结果
 */
@Data
public class AuthLoginResult<T> implements Serializable {
    /**
     * 错误码
     *
     * @see ErrorCode#getCode()
     */
    private Integer code;
    /**
     * 返回数据
     */
    private T loginfo;
    /**
     * 错误提示，用户可阅读
     *
     * @see ErrorCode#getMsg() ()
     */
    private String msg;

    public static <T> AuthLoginResult<T> authLogin(T data, String msg,Integer code) {
        AuthLoginResult<T> result = new AuthLoginResult<>();
        result.code = code;
        result.loginfo = data;
        result.msg = msg;
        return result;
    }

    /**
     * 将传入的 result 对象，转换成另外一个泛型结果的对象
     * <p>
     * 因为 A 方法返回的 CommonResult 对象，不满足调用其的 B 方法的返回，所以需要进行转换。
     *
     * @param result 传入的 result 对象
     * @param <T>    返回的泛型
     * @return 新的 CommonResult 对象
     */
    public static <T> AuthLoginResult<T> authError(AuthLoginResult<?> result) {
        return authError(result.getCode(), result.getMsg());
    }

    public static <T> AuthLoginResult<T> authError(Integer code, String message) {
        Assert.isTrue(!GlobalErrorCodeConstants.SUCCESS.getCode().equals(code), "code 必须是错误的！");
        AuthLoginResult<T> result = new AuthLoginResult<>();
        result.code = code;
        result.msg = message;
        return result;
    }

    public static <T> AuthLoginResult<T> authError(String message) {
        AuthLoginResult<T> result = new AuthLoginResult<>();
        result.msg = message;
        return result;
    }

    public static <T> AuthLoginResult<T> authError(ErrorCode errorCode) {
        return authError(errorCode.getCode(), errorCode.getMsg());
    }

    public static <T> AuthLoginResult<T> authSuccess(T data) {
        AuthLoginResult<T> result = new AuthLoginResult<>();
        result.code = GlobalErrorCodeConstants.SUCCESS.getCode();
        result.loginfo = data;
        result.msg = "";
        return result;
    }

    public static <T> AuthLoginResult<T> authSuccess(T data, String msg) {
        AuthLoginResult<T> result = new AuthLoginResult<>();
        result.code = GlobalErrorCodeConstants.SUCCESS.getCode();
        result.loginfo = data;
        result.msg = msg;
        return result;
    }

    public static <T> AuthLoginResult<T> authSuccessAndCodeIs1(T data, String msg) {
        AuthLoginResult<T> result = new AuthLoginResult<>();
        result.code = 1;
        result.loginfo = data;
        result.msg = msg;
        return result;
    }

    public static boolean isSuccess(Integer code) {
        return Objects.equals(code, GlobalErrorCodeConstants.SUCCESS.getCode());
    }

    @JsonIgnore // 避免 jackson 序列化
    public boolean isSuccess() {
        return isSuccess(code);
    }

    @JsonIgnore // 避免 jackson 序列化
    public boolean isError() {
        return !isSuccess();
    }

    /**
     * 判断是否有异常。如果有，则抛出 {@link ServiceException} 异常
     */
    public void checkError() throws ServiceException {
        if (isSuccess()) {
            return;
        }
        // 业务异常
        throw new ServiceException(code, msg);
    }

    /**
     * 判断是否有异常。如果有，则抛出 {@link ServiceException} 异常
     * 如果没有，则返回 {@link #logInfo} 数据
     */
    @JsonIgnore // 避免 jackson 序列化
    public T getCheckedData() {
        checkError();
        return loginfo;
    }
}
