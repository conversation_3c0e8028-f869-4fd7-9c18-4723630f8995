# 客户端IP获取和记录使用指南

## 概述

本系统提供了完整的客户端IP获取和记录解决方案，支持各种代理环境下的真实IP识别，并提供了灵活的日志记录机制。

## 功能特性

### 1. 增强的IP获取能力
- 支持多种代理头识别（X-Forwarded-For, X-Real-IP等）
- 自动处理多级代理场景
- 智能过滤无效IP地址
- IPv6地址标准化处理

### 2. 自动日志记录
- 全局拦截器自动记录客户端访问
- 支持详细和简要两种记录模式
- 可配置的URI模式匹配

### 3. 注解驱动
- `@ClientIPLog` 注解标记重要接口
- 支持动态启用/禁用
- 可自定义操作描述

## 使用方法

### 1. 基本IP获取

```java
// 获取客户端IP
String clientIP = ServletUtils.getClientIP();

// 获取详细IP信息
ClientIPUtils.ClientIPInfo ipInfo = ServletUtils.getClientIPInfo();
System.out.println("IP: " + ipInfo.getIp());
System.out.println("来源: " + ipInfo.getSource());
System.out.println("原始值: " + ipInfo.getRawValue());
```

### 2. 在Controller中使用注解

```java
@RestController
public class AuthController {
    
    @PostMapping("/login")
    @ClientIPLog(value = "用户登录", detailed = true)
    public CommonResult<AuthLoginRespVO> login(@RequestBody AuthLoginReqVO reqVO) {
        // 登录逻辑
        return success(authService.login(reqVO));
    }
}
```

### 3. 手动记录访问日志

```java
// 记录客户端访问
ServletUtils.logClientAccess("用户执行重要操作");

// 或者直接使用工具类
HttpServletRequest request = ServletUtils.getRequest();
ClientIPUtils.logClientAccess(request, "自定义操作");
```

### 4. 在请求中获取IP信息

```java
@RestController
public class MyController {
    
    @GetMapping("/api/data")
    public CommonResult<?> getData(HttpServletRequest request) {
        // 从请求属性中获取IP信息（由拦截器设置）
        ClientIPUtils.ClientIPInfo ipInfo = 
            (ClientIPUtils.ClientIPInfo) request.getAttribute("CLIENT_IP_INFO");
        String clientIP = (String) request.getAttribute("CLIENT_IP");
        
        // 业务逻辑
        return success(data);
    }
}
```

## 配置说明

### 1. 应用配置文件

在 `application.yml` 中添加：

```yaml
microservices:
  web:
    client-ip:
      # 是否启用客户端IP记录
      enable: true
      # 是否记录详细信息
      detailed: true
      # 需要详细记录的URI模式
      detailed-patterns:
        - "/admin-api/system/auth/"
        - "/app-api/member/auth/"
      # 需要跳过记录的URI模式  
      skip-patterns:
        - "/actuator/"
        - "/swagger-ui/"

# 日志级别配置
logging:
  level:
    mh.cloud.framework.common.util.servlet.ClientIPUtils: INFO
    mh.cloud.framework.web.core.interceptor.ClientIPInterceptor: INFO
```

### 2. 代理服务器配置

#### Nginx配置示例
```nginx
location / {
    proxy_pass http://backend;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

#### Apache配置示例
```apache
ProxyPreserveHost On
ProxyPass / http://backend/
ProxyPassReverse / http://backend/
ProxyAddHeaders On
```

## 支持的代理头

系统按优先级顺序检查以下HTTP头：

1. `X-Forwarded-For`
2. `X-Real-IP`
3. `X-Original-Forwarded-For`
4. `Proxy-Client-IP`
5. `WL-Proxy-Client-IP`
6. `HTTP_X_FORWARDED_FOR`
7. `HTTP_X_FORWARDED`
8. `HTTP_X_CLUSTER_CLIENT_IP`
9. `HTTP_CLIENT_IP`
10. `HTTP_FORWARDED_FOR`
11. `HTTP_FORWARDED`
12. `HTTP_VIA`
13. `REMOTE_ADDR`

## 测试工具

### 1. 运行IP获取测试
```bash
cd microservices-module-system/microservices-module-system-biz
mvn exec:java -Dexec.mainClass="mh.cloud.module.system.util.ClientIPTestTool"
```

### 2. 性能测试
```java
ClientIPTestTool.performanceTest();
```

### 3. 真实环境测试
```java
ClientIPTestTool.testRealEnvironment();
```

## 日志输出示例

### 详细模式日志
```
=== 客户端访问详细信息 ===
请求URI: /admin-api/system/auth/login
用户ID: user123
用户类型: 1
客户端IP: *************
IP来源: X-Forwarded-For
原始值: *************, ********
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
请求方法: POST
========================
```

### 简要模式日志
```
Client Access - User: user123, IP: *************, URI: POST /admin-api/system/data
```

## 常见问题

### 1. 获取到的IP是内网地址
- 检查代理服务器配置是否正确设置了转发头
- 确认代理服务器和应用服务器之间的网络配置

### 2. IP显示为127.0.0.1
- 通常是直接访问应用服务器导致的
- 检查是否通过代理访问

### 3. 性能考虑
- IP获取过程已优化，平均耗时小于1ms
- 大量并发时建议启用缓存机制

### 4. 安全考虑
- 代理头可能被伪造，在安全敏感场景下需要额外验证
- 建议结合其他安全措施使用

## 扩展开发

### 1. 自定义代理头
```java
// 在ClientIPUtils中添加新的代理头
private static final List<String> PROXY_HEADERS = Arrays.asList(
    "X-Forwarded-For",
    "X-Real-IP",
    "Your-Custom-Header" // 添加自定义头
);
```

### 2. 自定义拦截器
```java
@Component
public class CustomClientIPInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 自定义逻辑
        return true;
    }
}
```

### 3. 集成第三方IP库
```java
// 可以集成IP2Location、MaxMind等库获取地理位置信息
public static String getIPLocation(String ip) {
    // 实现IP地理位置查询
    return "北京市";
}
```
