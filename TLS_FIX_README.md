# SQL Server TLS 兼容性问题修复 - 最终解决方案

## 问题描述

遇到以下错误：
```
com.microsoft.sqlserver.jdbc.SQLServerException: 驱动程序无法通过使用安全套接字层(SSL)加密与 SQL Server 建立安全连接。
错误:"The server selected protocol version TLS10 is not accepted by client preferences [TLS13, TLS12]"
```

这个错误表明SQL Server使用TLS 1.0协议，但Java客户端只接受TLS 1.2和TLS 1.3。

## 最终修复方案（多层防护）

### 第一层：静态初始化配置
- 在`SystemServerApplication`类中添加静态初始化块
- 在类加载时就设置TLS配置，确保最早生效

### 第二层：应用程序启动配置
- 在main方法中再次确认TLS配置
- 创建自定义SSL上下文支持所有TLS版本

### 第三层：JDBC URL强制修改
- 在建立连接前强制修改SQL Server的JDBC URL
- 移除所有SSL相关参数，强制禁用SSL加密

### 第四层：JVM启动参数
- 通过启动脚本设置JVM参数
- 确保从JVM级别支持TLS 1.0

## 具体修复内容

### 1. 代码修改

#### 1.1 更新SQL Server JDBC连接字符串模板
- 文件：`SqlServiceImpl.java`
- 添加了更多SQL Server特定的连接参数
- 包括TLS协议配置和SSL设置

#### 1.2 增强SSL配置方法
- 文件：`SqlServiceImpl.java`
- 添加了`configureTLSContext()`方法
- 支持TLS 1.0-1.3所有版本

#### 1.3 更新全局SSL配置
- 文件：`SSLConfig.java`
- 添加了SQL Server特定的SSL配置
- 清空了禁用的算法列表

#### 1.4 增强连接建立过程
- 文件：`SQLHelperImpl.java`
- 在建立SQL Server连接前应用SSL配置
- 添加了`configureSSLForSQLServer()`方法

### 2. 配置文件

#### 2.1 TLS配置属性
- 文件：`tls-fix.properties`
- 包含所有必要的TLS和SSL系统属性

### 3. 测试工具

#### 3.1 TLS连接测试工具
- 文件：`TLSConnectionTest.java`
- 用于验证TLS配置是否正确
- 检查支持的TLS协议版本

#### 3.2 测试脚本
- 文件：`test-tls-fix.bat`
- 自动化测试脚本
- 设置必要的环境变量

## 使用方法

### 方法1：运行测试脚本
```bash
test-tls-fix.bat
```

### 方法2：手动设置JVM参数
在启动应用程序时添加以下JVM参数：
```bash
-Dhttps.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3
-Djdk.tls.client.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3
-Djdk.tls.disabledAlgorithms=
-Djdk.certpath.disabledAlgorithms=
-Dcom.microsoft.sqlserver.jdbc.fips=false
-Dcom.microsoft.sqlserver.jdbc.trustServerCertificate=true
-Dcom.microsoft.sqlserver.jdbc.encrypt=false
```

### 方法3：使用配置文件
将`tls-fix.properties`文件中的配置添加到系统属性中。

## 验证修复

### 1. 运行TLS测试
```bash
cd microservices-module-system/microservices-module-system-biz
mvn exec:java -Dexec.mainClass="mh.cloud.module.system.util.TLSConnectionTest"
```

### 2. 检查日志输出
查看应用程序日志，确认以下信息：
- "数据库SSL/TLS配置完成，已启用TLS 1.0-1.3支持"
- "SQL Server SSL配置完成"

### 3. 测试数据库连接
尝试执行需要数据库连接的操作，确认不再出现TLS错误。

## 安全注意事项

**重要：** 此修复方案为了兼容旧版SQL Server而禁用了某些SSL安全检查。在生产环境中使用时，请考虑以下安全建议：

1. **升级SQL Server**：建议升级到支持TLS 1.2+的SQL Server版本
2. **网络安全**：确保数据库服务器在安全的网络环境中
3. **证书验证**：在生产环境中启用适当的证书验证
4. **定期审查**：定期审查和更新SSL/TLS配置

## 故障排除

### 如果修复后仍然出现问题：

1. **检查SQL Server版本**
   ```sql
   SELECT @@VERSION
   ```

2. **检查SQL Server TLS配置**
   - 确认SQL Server支持的TLS版本
   - 检查SQL Server配置管理器中的协议设置

3. **启用SSL调试**
   添加JVM参数：`-Djavax.net.debug=ssl,handshake`

4. **检查JDBC驱动版本**
   确保使用兼容的Microsoft SQL Server JDBC驱动版本

5. **网络连接测试**
   使用telnet测试到SQL Server的网络连接：
   ```bash
   telnet <sql-server-host> <port>
   ```

## 相关文件

- `SqlServiceImpl.java` - 主要的SSL配置和连接字符串模板
- `SSLConfig.java` - 全局SSL配置
- `SQLHelperImpl.java` - 连接建立过程
- `TLSConnectionTest.java` - TLS测试工具
- `test-tls-fix.bat` - 自动化测试脚本
- `tls-fix.properties` - TLS配置属性文件
