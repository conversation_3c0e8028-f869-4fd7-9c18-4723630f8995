@echo off
echo ========================================
echo 数据库连接测试
echo ========================================
echo.

echo 设置TLS兼容性环境变量...
set JAVA_OPTS=-Xmx512m -Xms256m
set JAVA_OPTS=%JAVA_OPTS% -Dhttps.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3
set JAVA_OPTS=%JAVA_OPTS% -Djdk.tls.client.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3
set JAVA_OPTS=%JAVA_OPTS% -Djdk.tls.disabledAlgorithms=
set JAVA_OPTS=%JAVA_OPTS% -Djdk.certpath.disabledAlgorithms=
set JAVA_OPTS=%JAVA_OPTS% -Dcom.microsoft.sqlserver.jdbc.fips=false
set JAVA_OPTS=%JAVA_OPTS% -Dcom.microsoft.sqlserver.jdbc.trustServerCertificate=true
set JAVA_OPTS=%JAVA_OPTS% -Dcom.microsoft.sqlserver.jdbc.encrypt=false

echo 编译项目...
cd microservices-module-system\microservices-module-system-biz
call mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 运行数据库连接测试...
echo 这将测试不同的用户名、密码和连接配置组合
echo.

call mvn exec:java -Dexec.mainClass="mh.cloud.module.system.util.DatabaseConnectionTest" -Dexec.classpathScope=compile -Dexec.args="%JAVA_OPTS%"

echo.
echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 根据测试结果：
echo 1. 如果找到了成功的连接配置，请更新代码中的连接参数
echo 2. 如果所有连接都失败，请检查SQL Server配置和网络连接
echo 3. 特别注意用户名的拼写：kddbuse vs kdbbuse
echo.
pause
