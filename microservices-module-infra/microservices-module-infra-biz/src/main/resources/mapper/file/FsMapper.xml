<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mh.cloud.module.infra.dal.mysql.file.FsMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <insert id="insertFile" useGeneratedKeys="true" parameterType="mh.cloud.module.infra.dal.dataobject.file.FsFileDO">
        INSERT INTO F_FsFile (Name, ExtName, FileSize, CreateUser, CreateUserID, CreateTime, IsDeleted, Src, Guid, tenant_id)
        VALUES (#{file.Name}, #{file.ExtName}, #{file.FileSize}, #{file.CreateUser}, #{file.CreateUserID}, #{file.CreateTime}, 0, #{file.Src}, #{file.Guid}, 1)
    </insert>
</mapper>
