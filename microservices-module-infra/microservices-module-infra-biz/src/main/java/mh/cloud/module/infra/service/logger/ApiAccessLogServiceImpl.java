package mh.cloud.module.infra.service.logger;

import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.util.object.BeanUtils;
import mh.cloud.module.infra.api.logger.dto.ApiAccessLogCreateReqDTO;
import mh.cloud.module.infra.controller.admin.logger.vo.apiaccesslog.ApiAccessLogPageReqVO;
import mh.cloud.module.infra.dal.dataobject.logger.ApiAccessLogDO;
import mh.cloud.module.infra.dal.mysql.logger.ApiAccessLogMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;

/**
 * API 访问日志 Service 实现类
*/
@Slf4j
@Service
@Validated
@Data
public class ApiAccessLogServiceImpl implements ApiAccessLogService {

    @Resource
    private ApiAccessLogMapper apiAccessLogMapper;

    @Value("${microservices.access-log.enable}")
    private Boolean accessEnable;

    @Override
    public void createApiAccessLog(ApiAccessLogCreateReqDTO createDTO) {
        if (accessEnable) {
            ApiAccessLogDO apiAccessLog = BeanUtils.toBean(createDTO, ApiAccessLogDO.class);
            apiAccessLog.setRequestParams(StrUtil.maxLength(apiAccessLog.getRequestParams(), 1000));
            apiAccessLog.setResultMsg(StrUtil.maxLength(apiAccessLog.getResultMsg(), 1000));
            apiAccessLogMapper.insert(apiAccessLog);
        } else {
            log.info("访问日志： \n{}", createDTO.toString());
        }
    }

    @Override
    public PageResult<ApiAccessLogDO> getApiAccessLogPage(ApiAccessLogPageReqVO pageReqVO) {
        return apiAccessLogMapper.selectPage(pageReqVO);
    }

    @Override
    @SuppressWarnings("DuplicatedCode")
    public Integer cleanAccessLog(Integer exceedDay, Integer deleteLimit) {
        int count = 0;
        LocalDateTime expireDate = LocalDateTime.now().minusDays(exceedDay);
        // 循环删除，直到没有满足条件的数据
        for (int i = 0; i < Short.MAX_VALUE; i++) {
            int deleteCount = apiAccessLogMapper.deleteByCreateTimeLt(expireDate, deleteLimit);
            count += deleteCount;
            // 达到删除预期条数，说明到底了
            if (deleteCount < deleteLimit) {
                break;
            }
        }
        return count;
    }

}
