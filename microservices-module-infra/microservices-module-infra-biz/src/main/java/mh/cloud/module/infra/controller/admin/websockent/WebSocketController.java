package mh.cloud.module.infra.controller.admin.websockent;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.extra.spring.SpringUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.infra.websocket.WebSocket;
import mh.cloud.module.system.api.portal.SystemApi;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@RequestMapping("/infra/ws")
@RestController
@Tag(name = "管理后台 - socket消息发送")
public class WebSocketController {

    private final SystemApi systemApi = SpringUtil.getBean(SystemApi.class);


    @PostMapping("/httpSend")
    @Operation(summary = "socket消息发送,请求头携带token,参数sendMode=cluster  群发，sendMode=single 个体")
    public CommonResult<String> handleMessage(@RequestBody Map<String, Object> message, HttpServletRequest request) {

        if (!checkWhiteList(request)) {
            return CommonResult.error(403, "不允许该ip访问");
        }

        if (!message.containsKey("type") || !message.containsKey("userId")) {
            return CommonResult.error(400, "必须携带参数type,userId");
        }

        message.put("timestamp", System.currentTimeMillis() + "");

        if (message.containsKey("sendMode") && message.get("sendMode").toString().equals("cluster")) {
            WebSocket.sendMessage(null, message);
            return CommonResult.success("群发请求成功");
        }

        if (!message.containsKey("toUserId") || ObjUtil.isEmpty(message.get("toUserId"))) {
            return CommonResult.error(400, "单发必须携带参数toUserId,");
        }
        String toUserId = message.get("toUserId").toString();

        WebSocket.sendMessage(toUserId, message);
        return CommonResult.success("单体请求成功");
    }

    private boolean checkWhiteList(HttpServletRequest request) {
        String systemParameter = systemApi.getSettingValue("Socket");
        if (ObjUtil.isEmpty(systemParameter)) {
            //没有限制访问
            return true;
        }
        String[] split = systemParameter.split(",");
        List<String> ipList = Arrays.asList(split);

        String remoteAddr = request.getRemoteAddr();

        return ipList.contains(remoteAddr);
    }
}
