package mh.cloud.module.infra.dal.dataobject.file;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

@TableName("F_FsRootFolder")
@KeySequence("F_FsRootFolder") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
//文件根路径存储
public class FsRootFolderDO {
    /**
     * 路径id
     */
    @TableId(value = "ID")
    private String ID;
    /**
     * 更路径
     */
    @TableField("RootFolderPath")
    private String RootFolderPath;

    @TableField("UserName")
    private String UserName;

    @TableField("Pwd")
    private String Pwd;

    @TableField("CreateTime")
    private String CreateTime;
    /**
     * 是否有效
     */
    @TableField("IsValid")
    private String IsValid;
}
