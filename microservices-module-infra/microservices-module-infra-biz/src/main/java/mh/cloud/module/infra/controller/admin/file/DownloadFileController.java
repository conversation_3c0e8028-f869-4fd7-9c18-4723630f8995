package mh.cloud.module.infra.controller.admin.file;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.common.exception.ServiceException;
import mh.cloud.framework.security.core.LoginUser;
import mh.cloud.framework.security.core.util.SecurityFrameworkUtils;
import mh.cloud.module.infra.dal.dataobject.file.FsFileDO;
import mh.cloud.module.infra.service.file.FileService;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


@Tag(name = "管理后台 - 文件下载")
@RestController
@Slf4j
@RequestMapping("/downFile")
public class DownloadFileController {

    @Resource
    private FileService fileService;

    /**
     * 下载方式
     * a20009a5-ac0d-4509-bca5-c2294846db4c,5cfb51e5-9310-4bf5-8a74-9c81eb28bd84
     * 1、/FileManager/DownloadFile/FileToken=uid
     * 2、/BasicApplication/KindEditor/DownloadFile?path=/KindEditor/Uploadimage/20240927/20240927172037_8682.jpg
     * 3、/BasicApplication/DownloadFile?fileId=314475_0U1423J4-3.jpg
     */
    //javascript:void(0)
    @GetMapping("/FileManager/DownloadFile")
    @PermitAll
    @Operation(summary = "文件下载1")
    public void DownloadFile1(@RequestParam(name = "FileToken") String fileToken, HttpServletRequest request, HttpServletResponse response) throws Exception {

        if (ObjUtil.isEmpty(fileToken)) {
            throw new RuntimeException("需要文件下载参数。");
        }
        String[] split = fileToken.split(",");
        List<String> guid = Arrays.asList(split);
        List<FsFileDO> fsFileList = fileService.getFsFileList(guid);

        List<String> nameList = fsFileList.stream().map(FsFileDO::getName).collect(Collectors.toList());

        if (ObjUtil.isNotEmpty(nameList)) {
            StringBuilder sb = new StringBuilder();

            String userIp = request.getRemoteAddr();
            String userId = "";
            //如果图片访问不需要token，则用户不存在
            try {
                LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
                userId = loginUser.getId();
            } catch (Exception e) {
                log.error("用户信息不存在");
            }

            for (FsFileDO fsFileDO : fsFileList) {
                sb.append(DownLoadLogSql(fsFileDO, "DownloadFile", userIp, userId, request.getRequestURL().toString()));
            }
            //TODO 执行日志插入sql
            if (!sb.isEmpty()) {
                log.info("执行日志插入：{}", sb);
            }
        } else {
            log.error("文件token不正确");
            throw new ServiceException(500, "文件token不正确");
        }
        if (fsFileList.size() == 1) {
            String path = fsFileList.get(0).getFileFullPath();
            byte[] content = FileUtil.readBytes(path);
            if (content == null) {
                log.warn("[path({}) 文件不存在]", path);
                response.setStatus(HttpStatus.NOT_FOUND.value());
                return;
            }
            downloadFile(path, getResultFileName(fsFileList.get(0), request, false), response);
        } else {
            //将第一个文件作为压缩包名
            downloadZipFile(fsFileList, getResultFileName(fsFileList.get(0), request, true), response, request);
        }
    }

    @GetMapping("/BasicApplication/KindEditor/DownloadFile")
    @PermitAll
    @Operation(summary = "文件下载2")
    public void DownloadFile2(@RequestParam String path, HttpServletResponse response) throws IOException {
        downloadFile(path, path.substring(path.lastIndexOf("/")), response);
    }

    @GetMapping("/BasicApplication/DownloadFile")
    @PermitAll
    @Operation(summary = "文件下载3")
    public void DownloadFile3(@RequestParam(name = "FileID") String fileId,
                              HttpServletResponse response,
                              HttpServletRequest request) throws Exception {
        if (ObjUtil.isEmpty(fileId)) {
            throw new ServiceException(500, "需要文件下载参数[FileID]");
        }
        List<String> fileNames = Arrays.stream(fileId.split(",")).toList();
        List<String> fileIds = fileNames.stream().map(f -> f.split("_")[0]).toList();
        List<FsFileDO> fsFileList = fileService.getFsFileByIds(fileIds);
        if (ObjUtil.isEmpty(fsFileList)) {
            throw new ServiceException(500, "数据异常,没有找到对应文件信息" + fileIds);
        }
        //单个文件下载
        if (fsFileList.size() == 1) {
            FsFileDO fsFileDO = fsFileList.get(0);
            //下载文件
            downloadFile(fsFileDO.getFileFullPath(), getResultFileName(fsFileDO, request, false), response);
        } else {
            downloadZipFile(fsFileList, getResultFileName(fsFileList.get(0), request, true), response, request);
        }
    }

    /**
     * 下载日志sql
     */
    private String DownLoadLogSql(FsFileDO file, String optnType, String userIp, String userId, String url) {
        return "INSERT INTO NK_Log.dbo.T_Files_DownloadLog (ID, CreateUser, CreateUserID, CreateTime, DeptID, DeptName, OptnType, UserName, UserID, UserIP, Url, FileID, FileName, ExtName, Src, WorkNo) " +
                "SELECT NEWID(),Name,ID,GETDATE(),DeptID,DeptName,'" + optnType + "',Name,ID,'" + userIp + "','" + url + "','" + userId + "','" + file.getName() + "','" + file.getExtName() + "','" + file.getSrc() + "', WorkNo FROM dbo.A_User WHERE ID = '" + userId + "'";

    }

    /**
     * 获取文件名，多个文件.zip做后缀
     *
     * @param file 文件
     * @return 文件名
     */
    private String getResultFileName(FsFileDO file, HttpServletRequest request, boolean isZip) {
        //取第一个文件作为压缩包文件名
        String fileName = file.getName();

        if (isZip) {
            if (Paths.get(fileName).getFileName().toString().contains(".")) {
                fileName = Paths.get(fileName).getFileName().toString().substring(0, Paths.get(fileName).getFileName().toString().lastIndexOf('.'));
            }
            fileName += ".zip";
        }
        //文件有下划线，就取下划线之后部分
        String realFileName = fileName.indexOf('_') > 0 ? fileName.substring(fileName.indexOf('_') + 1) : fileName;

        // 获取浏览器名称并转换为大写
        String explorerName = request.getHeader("User-Agent").toUpperCase();

        // 判断是否为 IE 浏览器或包含 rv:11
        if (explorerName.contains("IE") || explorerName.contains("INTERNETEXPLORER") || request.getHeader("User-Agent").contains("rv:11")) {
            // 使用 URL 编码将文件名进行编码，指定编码为 UTF-8
            realFileName = URLEncoder.encode(realFileName, StandardCharsets.UTF_8);
            // 将空格替换为 %20
            realFileName = realFileName.replace("+", "%20");
        }
        // 将文件名中的空格替换为 %20
        realFileName = realFileName.replace(" ", "%20");

        return realFileName;
    }


    public void downloadZipFile(List<FsFileDO> fileDOList, String fileName, HttpServletResponse response, HttpServletRequest request) throws Exception {
        //创建一个临时文件
        ZipOutputStream zipOut = new ZipOutputStream(new FileOutputStream(fileName));
        for (FsFileDO fsFileDO : fileDOList) {
            byte[] bytes = FileUtil.readBytes(fsFileDO.getFileFullPath());
            String name = getResultFileName(fsFileDO, request, false);
            fileToZip(bytes, name, zipOut);
        }
        zipOut.close();
        //下载压缩文件
        downloadFile(fileName, fileName, response);

        //下载完成之后，删掉这个zip包
        File fileTempZip = new File(fileName);
        if (!fileTempZip.delete()) {
            log.error("临时压缩文件删除出错");
        }
    }

    /**
     * 压缩文件
     */
    public void fileToZip(byte[] fileByte, String fileName, ZipOutputStream zipOut) throws IOException {
        // 需要压缩的文件
        // 获取文件名称,如果有特殊命名需求,可以将参数列表拓展,传fileName
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(fileByte);
        // 缓冲
        byte[] bufferArea = new byte[1024 * 10];
        BufferedInputStream bufferStream = new BufferedInputStream(byteArrayInputStream, 1024 * 10);
        // 将当前文件作为一个zip实体写入压缩流,fileName代表压缩文件中的文件名称
        zipOut.putNextEntry(new ZipEntry(fileName));
        int length = 0;
        // 最常规IO操作,不必紧张
        while ((length = bufferStream.read(bufferArea, 0, 1024 * 10)) != -1) {
            zipOut.write(bufferArea, 0, length);
        }
        //关闭流
        byteArrayInputStream.close();
        // 需要注意的是缓冲流必须要关闭流,否则输出无效
        bufferStream.close();
    }


    private void downloadFile(String filePath, String fileName, HttpServletResponse response) throws IOException {
        File file = new File(filePath);

        // 设置响应头
        response.setContentType("application/octet-stream");
        response.setHeader("Charset", "UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

        // 输出文件内容
        try (OutputStream out = response.getOutputStream();
             FileInputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = fis.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}
