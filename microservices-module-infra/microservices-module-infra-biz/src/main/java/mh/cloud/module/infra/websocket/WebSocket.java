package mh.cloud.module.infra.websocket;


import cn.hutool.core.util.ObjUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.module.system.api.oauth2.OAuth2TokenApi;
import mh.cloud.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

//注册成组件
@Component
//定义websocket服务器端，它的功能主要是将目前的类定义成一个websocket服务器端。注解的值将被用于监听用户连接的终端访问URL地址
@ServerEndpoint("/infra/websocket")
//如果不想每次都写private final Logger logger = LoggerFactory.getLogger(当前类名.class); 可以用注解@Slf4j;可以直接调用log.info
@Slf4j
public class WebSocket {


    //存放websocket的集合（本次demo不会用到，聊天室的demo会用到）
    private static final ConcurrentMap<String, Session> webSocketMap = new ConcurrentHashMap<>();

    private final OAuth2TokenApi oauth2TokenApi = SpringUtil.getBean(OAuth2TokenApi.class);


    //前端请求时一个websocket时
    @OnOpen
    public void onOpen(Session session) {
        String queryString = session.getQueryString();
        String userId = checkTokenAndGetUserId(getToken(queryString));
        webSocketMap.put(userId, session);
        log.info("【websocket消息】有新的连接, 总数:{}", webSocketMap.size());
    }

    //前端关闭时一个websocket时
    @OnClose
    public void onClose(Session session) {
        String queryString = session.getQueryString();
        String userId = checkTokenAndGetUserId(getToken(queryString));
        webSocketMap.remove(userId);
        log.info("【websocket消息】连接断开, 总数:{}", webSocketMap.size());
    }

    //前端向后端发送消息
    @OnMessage
    public void onMessage(String message, Session session) {
        try {
            String queryString = session.getQueryString();
            String userId = checkTokenAndGetUserId(getToken(queryString));
            // 心跳机制处理断开连接
            if (!webSocketMap.containsKey(userId)) {
                webSocketMap.put(userId, session);
            }
            if (message.equals("ping")) {
                session.getAsyncRemote().sendText("pong");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    //新增一个方法用于主动向客户端发送消息
    public static void sendMessage(String toUserId, Object message) {
        String jsonStr = JSONUtil.toJsonStr(message);
        if (ObjUtil.isEmpty(toUserId)) {
            webSocketMap.forEach((userId, session) -> {
                try {
                    session.getAsyncRemote().sendText(jsonStr);
                } catch (Exception e) {
                    log.warn("发送给用户：{},socket异常，信息：{}", userId, jsonStr);
                }
            });
        } else {
            Session session = webSocketMap.get(toUserId);
            try {
                session.getAsyncRemote().sendText(jsonStr);
            } catch (Exception e) {
                log.warn("发送给用户：{},socket异常，信息：{}，异常信息：{}", toUserId, message, e.getMessage());
            }
        }
    }

    private String getToken(String queryString) {
        if (queryString.contains("&")) {
            String[] split = queryString.split("&");
            for (String parm : split) {
                String[] split1 = parm.split("=");
                if (split1[0].equals("token")) {
                    return split1[1].toString();
                }
            }
        } else {
            return queryString.split("=")[1];
        }
        return null;
    }

    private String checkTokenAndGetUserId(String token) {
        OAuth2AccessTokenCheckRespDTO accessToken = oauth2TokenApi.checkAccessToken(token).getCheckedData();

        if (ObjUtil.isEmpty(accessToken.getUserId())) {
            log.error("token校验异常用户信息不存在");
        }
        return accessToken.getUserId();
    }

}
