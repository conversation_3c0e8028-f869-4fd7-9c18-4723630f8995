@echo off
echo ========================================
echo 客户端IP功能测试（解决循环依赖）
echo ========================================
echo.

echo [1/3] 清理并编译项目...
cd microservices-module-system\microservices-module-system-biz
call mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo 编译失败！请检查循环依赖是否已解决。
    pause
    exit /b 1
)

echo [2/3] 运行客户端IP测试工具...
call mvn exec:java -Dexec.mainClass="mh.cloud.module.system.util.ClientIPTestTool" -Dexec.classpathScope=compile -q

echo [3/3] 启动应用程序测试...
echo 注意观察控制台输出中的客户端IP记录信息
echo.

set JAVA_OPTS=-Xmx1024m -Xms512m
set JAVA_OPTS=%JAVA_OPTS% -Dhttps.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3
set JAVA_OPTS=%JAVA_OPTS% -Djdk.tls.client.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3
set JAVA_OPTS=%JAVA_OPTS% -Djdk.tls.disabledAlgorithms=
set JAVA_OPTS=%JAVA_OPTS% -Djdk.certpath.disabledAlgorithms=
set JAVA_OPTS=%JAVA_OPTS% -Dcom.microsoft.sqlserver.jdbc.fips=false
set JAVA_OPTS=%JAVA_OPTS% -Dcom.microsoft.sqlserver.jdbc.trustServerCertificate=true
set JAVA_OPTS=%JAVA_OPTS% -Dcom.microsoft.sqlserver.jdbc.encrypt=false

call mvn spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%" -Dspring-boot.run.profiles=local

echo.
echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 修复说明:
echo 1. 已解决模块循环依赖问题
echo 2. 客户端IP拦截器移至common模块
echo 3. 创建了条件化配置支持Security集成
echo 4. 保持了所有原有功能
echo.
echo 如果应用程序成功启动，说明循环依赖问题已解决！
echo.
pause
