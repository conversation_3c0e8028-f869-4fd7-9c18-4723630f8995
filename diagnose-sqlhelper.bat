@echo off
echo ========================================
echo SQLHelper 问题诊断脚本
echo ========================================
echo.

cd microservices-module-system\microservices-module-system-biz

echo [诊断1] 检查Java版本...
java -version
echo.

echo [诊断2] 检查Maven版本...
mvn -version
echo.

echo [诊断3] 检查端口占用情况...
echo 检查常用端口占用:
netstat -ano | findstr :6390
netstat -ano | findstr :6391
netstat -ano | findstr :6392
netstat -ano | findstr :6399
netstat -ano | findstr :48090
echo.

echo [诊断4] 检查项目结构...
echo 检查关键文件是否存在:
if exist "src\main\java\mh\cloud\module\system\util\test.java" (
    echo ✓ test.java 存在
) else (
    echo ✗ test.java 不存在
)

if exist "src\main\resources\application-testlocal.yaml" (
    echo ✓ application-testlocal.yaml 存在
) else (
    echo ✗ application-testlocal.yaml 不存在
)

if exist "src\main\java\mh\cloud\module\system\service\db\SQLHelper.java" (
    echo ✓ SQLHelper.java 存在
) else (
    echo ✗ SQLHelper.java 不存在
)
echo.

echo [诊断5] 尝试编译项目...
call mvn compile -X > compile.log 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✓ 编译成功
) else (
    echo ✗ 编译失败，查看 compile.log 获取详细信息
    echo 最后几行错误信息:
    tail -n 20 compile.log 2>nul || (
        echo 无法显示错误信息，请查看 compile.log 文件
    )
)
echo.

echo [诊断6] 检查依赖...
echo 检查关键依赖是否存在:
call mvn dependency:tree | findstr "spring-boot-starter" > deps.log 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✓ Spring Boot 依赖存在
) else (
    echo ✗ Spring Boot 依赖可能有问题
)
echo.

echo ========================================
echo 诊断完成！
echo 如果发现问题，请根据上述信息进行修复。
echo ========================================
pause
