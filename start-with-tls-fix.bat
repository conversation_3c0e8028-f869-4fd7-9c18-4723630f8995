@echo off
echo ========================================
echo 启动应用程序（已修复SQL Server TLS兼容性）
echo ========================================
echo.

echo 设置TLS兼容性环境变量...
set JAVA_OPTS=-Xmx1024m -Xms512m
set JAVA_OPTS=%JAVA_OPTS% -Dhttps.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3
set JAVA_OPTS=%JAVA_OPTS% -Djdk.tls.client.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3
set JAVA_OPTS=%JAVA_OPTS% -Djdk.tls.disabledAlgorithms=
set JAVA_OPTS=%JAVA_OPTS% -Djdk.certpath.disabledAlgorithms=
set JAVA_OPTS=%JAVA_OPTS% -Dcom.microsoft.sqlserver.jdbc.fips=false
set JAVA_OPTS=%JAVA_OPTS% -Dcom.microsoft.sqlserver.jdbc.trustServerCertificate=true
set JAVA_OPTS=%JAVA_OPTS% -Dcom.microsoft.sqlserver.jdbc.encrypt=false
set JAVA_OPTS=%JAVA_OPTS% -Dcom.sun.net.ssl.checkRevocation=false
set JAVA_OPTS=%JAVA_OPTS% -Djsse.enableSNIExtension=false

echo 当前JVM参数:
echo %JAVA_OPTS%
echo.

echo 选择要启动的模块:
echo 1. System Server (端口: 48090)
echo 2. Business Server (端口: 48099)
echo 3. Infra Server (端口: 48082)
echo 4. Gateway Server (端口: 48080)
echo 5. 全部启动
echo.

set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" goto start_system
if "%choice%"=="2" goto start_business
if "%choice%"=="3" goto start_infra
if "%choice%"=="4" goto start_gateway
if "%choice%"=="5" goto start_all
echo 无效选择，退出...
goto end

:start_system
echo 启动 System Server...
cd microservices-module-system\microservices-module-system-biz
call mvn spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%" -Dspring-boot.run.profiles=local
goto end

:start_business
echo 启动 Business Server...
cd microservices-module-business\microservices-module-business-biz
call mvn spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%" -Dspring-boot.run.profiles=local
goto end

:start_infra
echo 启动 Infra Server...
cd microservices-module-infra\microservices-module-infra-biz
call mvn spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%" -Dspring-boot.run.profiles=local
goto end

:start_gateway
echo 启动 Gateway Server...
cd microservices-gateway
call mvn spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%" -Dspring-boot.run.profiles=local
goto end

:start_all
echo 启动所有服务...
echo.
echo 启动 System Server...
start /B cmd /C "cd microservices-module-system\microservices-module-system-biz && mvn spring-boot:run -Dspring-boot.run.jvmArguments=\"%JAVA_OPTS%\" -Dspring-boot.run.profiles=local"

timeout /t 10 /nobreak >nul

echo 启动 Business Server...
start /B cmd /C "cd microservices-module-business\microservices-module-business-biz && mvn spring-boot:run -Dspring-boot.run.jvmArguments=\"%JAVA_OPTS%\" -Dspring-boot.run.profiles=local"

timeout /t 10 /nobreak >nul

echo 启动 Infra Server...
start /B cmd /C "cd microservices-module-infra\microservices-module-infra-biz && mvn spring-boot:run -Dspring-boot.run.jvmArguments=\"%JAVA_OPTS%\" -Dspring-boot.run.profiles=local"

timeout /t 10 /nobreak >nul

echo 启动 Gateway Server...
start /B cmd /C "cd microservices-gateway && mvn spring-boot:run -Dspring-boot.run.jvmArguments=\"%JAVA_OPTS%\" -Dspring-boot.run.profiles=local"

echo 所有服务启动命令已执行，请查看各个窗口的启动状态
goto end

:end
echo.
echo ========================================
echo 启动完成！
echo ========================================
echo.
echo 说明:
echo - 已应用SQL Server TLS 1.0兼容性修复
echo - 如果仍然遇到TLS错误，请检查SQL Server配置
echo - 可以通过添加 -Djavax.net.debug=ssl 参数来调试SSL问题
echo.
pause
