@echo off
echo ========================================
echo SQLHelper SSL/TLS 测试脚本
echo ========================================
echo.

cd microservices-module-system\microservices-module-system-biz

echo [1/5] 设置环境变量（包含SSL配置）...
set SPRING_PROFILES_ACTIVE=testlocal
set MAVEN_OPTS=-Xmx512m -XX:MaxMetaspaceSize=256m -Dhttps.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3 -Djdk.tls.client.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3 -Dcom.sun.net.ssl.checkRevocation=false

echo [2/5] 清理并编译项目...
call mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo [3/5] 运行SSL配置测试...
call mvn exec:java -Dexec.mainClass="mh.cloud.module.system.util.SSLTestTool" -Dexec.classpathScope=compile -q
if %ERRORLEVEL% neq 0 (
    echo SSL测试失败！
    echo.
)

echo [4/5] 运行离线测试（完全不依赖外部服务）...
call mvn exec:java -Dexec.mainClass="mh.cloud.module.system.util.SQLHelperOfflineTest" -Dexec.classpathScope=compile -q
if %ERRORLEVEL% neq 0 (
    echo 离线测试失败！
    pause
    exit /b 1
)

echo [5/5] 运行加密工具测试（包含SSL配置和数据库连接）...
call mvn exec:java -Dexec.mainClass="mh.cloud.module.system.util.SymmetricEncryptionUtils" -Dexec.classpathScope=compile -q
if %ERRORLEVEL% neq 0 (
    echo 加密工具测试失败！
    echo 这可能是由于数据库连接问题导致的。
    echo.
)

echo.
echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 测试结果说明:
echo - SSL配置测试: 验证SSL/TLS协议支持
echo - 离线测试: 验证不依赖外部服务的功能
echo - 加密工具测试: 验证完整的数据库连接功能
echo.
pause
