@echo off
echo ========================================
echo 最终SQL Server TLS兼容性修复测试
echo ========================================
echo.

echo 设置强制TLS兼容性环境变量...
set JAVA_OPTS=-Xmx1024m -Xms512m
set JAVA_OPTS=%JAVA_OPTS% -Dhttps.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3
set JAVA_OPTS=%JAVA_OPTS% -Djdk.tls.client.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3
set JAVA_OPTS=%JAVA_OPTS% -Djdk.tls.disabledAlgorithms=
set JAVA_OPTS=%JAVA_OPTS% -Djdk.certpath.disabledAlgorithms=
set JAVA_OPTS=%JAVA_OPTS% -Dcom.microsoft.sqlserver.jdbc.fips=false
set JAVA_OPTS=%JAVA_OPTS% -Dcom.microsoft.sqlserver.jdbc.trustServerCertificate=true
set JAVA_OPTS=%JAVA_OPTS% -Dcom.microsoft.sqlserver.jdbc.encrypt=false
set JAVA_OPTS=%JAVA_OPTS% -Dcom.sun.net.ssl.checkRevocation=false
set JAVA_OPTS=%JAVA_OPTS% -Djsse.enableSNIExtension=false

echo 当前JVM参数:
echo %JAVA_OPTS%
echo.

echo 编译项目...
cd microservices-module-system\microservices-module-system-biz
call mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 启动应用程序（带TLS修复）...
echo 请注意观察控制台输出中的TLS配置信息
echo.

call mvn spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%" -Dspring-boot.run.profiles=local

echo.
echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 修复说明:
echo 1. 已在应用程序启动类中添加静态TLS配置
echo 2. 已强制修改SQL Server JDBC URL禁用SSL
echo 3. 已设置JVM级别的TLS兼容性参数
echo 4. 已创建自定义SSL上下文支持所有TLS版本
echo.
echo 如果仍然遇到问题，请检查:
echo - SQL Server是否正在运行
echo - 网络连接是否正常
echo - 数据库连接字符串是否正确
echo.
pause
