@echo off
echo ========================================
echo 完整的SQL Server连接修复测试
echo ========================================
echo.

echo 设置完整的TLS兼容性环境变量...
set JAVA_OPTS=-Xmx1024m -Xms512m
set JAVA_OPTS=%JAVA_OPTS% -Dhttps.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3
set JAVA_OPTS=%JAVA_OPTS% -Djdk.tls.client.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3
set JAVA_OPTS=%JAVA_OPTS% -Djdk.tls.disabledAlgorithms=
set JAVA_OPTS=%JAVA_OPTS% -Djdk.certpath.disabledAlgorithms=
set JAVA_OPTS=%JAVA_OPTS% -Dcom.microsoft.sqlserver.jdbc.fips=false
set JAVA_OPTS=%JAVA_OPTS% -Dcom.microsoft.sqlserver.jdbc.trustServerCertificate=true
set JAVA_OPTS=%JAVA_OPTS% -Dcom.microsoft.sqlserver.jdbc.encrypt=false
set JAVA_OPTS=%JAVA_OPTS% -Dcom.sun.net.ssl.checkRevocation=false
set JAVA_OPTS=%JAVA_OPTS% -Djsse.enableSNIExtension=false

echo 当前JVM参数:
echo %JAVA_OPTS%
echo.

echo [步骤1] 编译项目...
cd microservices-module-system\microservices-module-system-biz
call mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo [步骤2] 运行TLS配置验证...
echo 验证TLS配置是否正确...
call mvn exec:java -Dexec.mainClass="mh.cloud.module.system.util.TLSVerificationTool" -Dexec.classpathScope=compile -q

echo.
echo [步骤3] 运行数据库连接测试...
echo 测试不同的用户名和连接配置...
call mvn exec:java -Dexec.mainClass="mh.cloud.module.system.util.DatabaseConnectionTest" -Dexec.classpathScope=compile -q

echo.
echo [步骤4] 启动应用程序进行完整测试...
echo 注意观察控制台输出中的以下信息:
echo - "静态初始化: 配置TLS设置以支持SQL Server TLS 1.0"
echo - "TLS配置完成，已启用TLS 1.0-1.3支持"
echo - "已强制禁用SQL Server SSL"
echo - "已修正用户名从 kdbbuse 到 kddbuse"
echo.

echo 按任意键启动应用程序，或按Ctrl+C取消...
pause

call mvn spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%" -Dspring-boot.run.profiles=local

echo.
echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 修复总结:
echo.
echo 1. TLS兼容性修复:
echo    - 静态初始化块在类加载时设置TLS配置
echo    - 支持TLS 1.0-1.3所有版本
echo    - 清空禁用算法列表
echo    - 创建自定义SSL上下文
echo.
echo 2. JDBC URL修复:
echo    - 强制禁用SQL Server SSL加密
echo    - 移除复杂的TLS协议配置
echo    - 设置encrypt=false和trustServerCertificate=true
echo.
echo 3. 用户认证修复:
echo    - 自动修正用户名拼写错误 (kdbbuse -> kddbuse)
echo    - 支持多个用户名尝试连接
echo    - 详细的连接日志输出
echo.
echo 4. 多层防护:
echo    - JVM启动参数级别
echo    - 应用程序启动级别  
echo    - 连接建立级别
echo    - 用户名修正级别
echo.
echo 如果应用程序成功启动且没有TLS或用户认证错误，
echo 说明修复已经成功！
echo.
pause
