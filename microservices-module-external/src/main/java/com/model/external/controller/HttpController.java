package com.model.external.controller;

import cn.hutool.core.util.ObjUtil;
import com.model.external.common.Result;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("external/http")
public class HttpController {

    @GetMapping("/get")
    public Result<String> testGet(@RequestParam String id, HttpServletRequest request) {
        if (id.equals("2")) {
            return Result.fail("id不符合要求");
        }
        String token = request.getHeader("Token");
        if (ObjUtil.isEmpty(token)) {
            return Result.fail(401, "token不存在");
        }
        return Result.ok(id);
    }

    @PostMapping("/post")
    public Result<String> testPost(@RequestParam String id, HttpServletRequest request) {
        String token = request.getHeader("Token");
        if (id.equals("2")) {
            return Result.fail("id不符合要求");
        }
        if (ObjUtil.isEmpty(token)) {
            return Result.fail(401, "token不存在");
        }
        return Result.ok(id + "_" + token);
    }


}
