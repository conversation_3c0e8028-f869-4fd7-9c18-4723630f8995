package com.model.external.intercept;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Arrays;
import java.util.List;

@Component
@Slf4j
public class AuthInterceptor implements HandlerInterceptor {

    @Value("${auth.whites}")
    private String whites;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String[] split = whites.split(",");
        List<String> list = Arrays.asList(split);
        //属于gateway放行白名单
        String remoteAddr = request.getRemoteAddr();
        if (list.contains(remoteAddr)) {
            log.info("访问ip：{}\n目标地址：{}\n请求方式：{}，是否放行：{}", remoteAddr, request.getRequestURI(), request.getMethod(), true);
            return true;
        }
        //设置响应状态为401
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        //拦截请求
        log.info("\n访问ip：{}\n目标地址：{}\n请求方式：{}\n是否放行：{}", remoteAddr, request.getRequestURI(), request.getMethod(), false);
        return false;
    }
}
