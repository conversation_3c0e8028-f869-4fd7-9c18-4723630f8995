spring:
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Feign 等会存在重复定义的服务
  #  # Servlet 配置
  servlet:
    multipart:
      max-file-size: 16MB # 单个文件大小
      max-request-size: 32MB # 设置总上传的文件大小
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER # 解决 SpringFox 与 SpringBoot 2.6.x 不兼容的问题，参见 SpringFoxHandlerProviderBeanPostProcessor 类
  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 LocalDateTime 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 **********.401，而是直接 **********401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean
  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时
  data:
    redis:
      host: 127.0.0.1 # 地址
      port: 6399 # 端口
      database: 1 # 数据库索
microservices:
  info:
    version: 1.0.0
    base-package: mh.cloud.module.business
  web:
    admin-ui:
      url: http://dashboard.microservices.iocoder.cn # Admin 管理后台 UI 的地址
  swagger:
    title: 管理后台
    description: 提供管理员管理的所有功能
    version: ${microservices.info.version}
    base-package: ${microservices.info.base-package}
  codegen:
    base-package: mh.cloud
    db-schemas: ${spring.datasource.dynamic.datasource.master.name}
    front-type: 10 # 前端模版的类型，参见 CodegenFrontTypeEnum 枚举类
  tenant: # 多租户相关配置项
    enable: true
    ignore-urls:
      - /admin-api/infra/file/*/get/** # 获取图片，和租户无关
      - /admin-api/downFile/**
      - /admin-api/business/crawler/**
      - /admin-api/quartz/job-log/**
    ignore-tables:
      - system_job
      - system_job_log
      - system_job_api
      - C_NewTimerTask
      - C_NewTimerTask_ApiSQL
      - C_NewTimerTask_ApiUrl
debug: false
server:
  tongweb:
    license:
      path=classpath: license.dat
com:
  tongtech:
    dino:
      client:
        enabled: true
        url: http://localhost:8083/dino
        username: admin
        password: <EMAIL>
