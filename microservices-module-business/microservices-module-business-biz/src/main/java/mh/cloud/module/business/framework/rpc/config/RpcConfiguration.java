package mh.cloud.module.business.framework.rpc.config;

import mh.cloud.module.infra.api.file.FileApi;
import mh.cloud.module.infra.api.logger.ApiErrorLogApi;
import mh.cloud.module.system.api.portal.SystemApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {FileApi.class, SystemApi.class, ApiErrorLogApi.class})
public class RpcConfiguration {
}
