package mh.cloud.module.business.controller.admin.user;

import jakarta.annotation.Resource;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.business.service.bususerexts.impl.AUserExtsServiceImpl;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/MHBus")
/**
 * 2025-6-20 spc
 * 自定义处理用户数据类
 */
public class BusinessUserController {

    @Resource
    private AUserExtsServiceImpl aUserExtsService;


    /**
     * 批量处理用户签名和头像数据
     * 接口方法，接收token并执行整个流程。
     * @param token 上传文件所需的token
     * @return 返回操作结果
     */
    @RequestMapping ("/user/userImgsSync")
    public CommonResult<Map<String, Object>> processSignAndHead(@RequestParam String token) {
        System.out.println("接收到的token：" + token);
        // 调用AUserExtsServiceImpl中的processAndUploadImages方法，并传递token
        Map<String,Object> map = new HashMap<>();
        map.put("token",token);
        aUserExtsService.setUploadToken(token);
        //return CommonResult.success(map);
        return CommonResult.success(aUserExtsService.processAndUploadImages());
    }
}
