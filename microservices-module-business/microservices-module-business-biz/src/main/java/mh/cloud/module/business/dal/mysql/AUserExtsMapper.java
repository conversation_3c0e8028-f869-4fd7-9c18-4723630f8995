package mh.cloud.module.business.dal.mysql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import mh.cloud.module.business.dal.dataobject.AUserExtsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

@Mapper
public interface AUserExtsMapper extends BaseMapper<AUserExtsDO> {

    /**
     * 查询A_UserExts中UserID不在A_User表中的记录
     * @return 无效的A_UserExts记录列表
     */
    @Select("SELECT * FROM A_UserExts ue WHERE NOT EXISTS (SELECT 1 FROM A_User u WHERE u.ID = ue.UserID)")
    List<AUserExtsDO> selectOrphanedRecords();

    /**
     * 删除A_UserExts中UserID不在A_User表中的记录
     * @return 删除的记录数量
     */
    @Delete("DELETE FROM A_UserExts WHERE NOT EXISTS (SELECT 1 FROM A_User u WHERE u.ID = UserID)")
    int deleteOrphanedRecords();

    /**
     * 查询A_User表中所有有效用户的ID（分页）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 用户ID列表
     */
    @Select("SELECT ID FROM A_User WHERE ISNULL(IsDeleted, '0') = '0' ORDER BY ID OFFSET #{offset} ROWS FETCH NEXT #{limit} ROWS ONLY")
    List<String> selectValidUserIds(int offset, int limit);

    /**
     * 统计A_User表中有效用户的总数
     * @return 有效用户总数
     */
    @Select("SELECT COUNT(*) FROM A_User WHERE ISNULL(IsDeleted, '0') = '0'")
    long countValidUsers();
}