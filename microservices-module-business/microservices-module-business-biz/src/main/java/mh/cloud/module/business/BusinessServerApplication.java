package mh.cloud.module.business;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 项目的启动类
*/
@SpringBootApplication
@EnableFeignClients
public class BusinessServerApplication extends SpringBootServletInitializer {
    // 重写 configure 方法，支持打包成 war 包
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(BusinessServerApplication.class);
    }
    public static void main(String[] args) {
        SpringApplication.run(BusinessServerApplication.class, args);
        System.out.println("=== Business 启动成功 (^_^) ===");
    }

}
