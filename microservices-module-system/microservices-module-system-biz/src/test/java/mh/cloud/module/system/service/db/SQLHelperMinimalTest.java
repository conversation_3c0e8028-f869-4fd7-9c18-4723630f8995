package mh.cloud.module.system.service.db;

import lombok.extern.slf4j.Slf4j;
import mh.cloud.module.system.SystemServerApplication;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * SQLHelper 最小化测试类
 * 排除Redis等外部依赖，专注测试SQLHelper核心功能
 * 
 * @Author: ytq
 * @Date: 2025/7/29 11:00
 * @Description: SQLHelper 最小化功能测试
 */
@SpringBootTest(classes = SystemServerApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@TestPropertySource(properties = {
    "spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration," +
        "org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration," +
        "mh.cloud.framework.redis.config.MicroservicesRedisAutoConfiguration," +
        "mh.cloud.framework.redis.config.MicroservicesCacheAutoConfiguration",
    "microservices.captcha.enable=false",
    "microservices.security.mock-enable=true",
    "microservices.xss.enable=false",
    "microservices.demo=false",
    "management.health.redis.enabled=false",
    "spring.cache.type=none"
})
@Slf4j
public class SQLHelperMinimalTest {

    @Test
    public void testStaticMethods() {
        log.info("开始测试SQLHelper静态方法...");
        
        // 测试格式化方法
        String formattedSql = SQLHelper.format("SELECT * FROM table WHERE id = {0} AND name = {1}", "123", "test");
        log.info("格式化SQL: {}", formattedSql);
        assert formattedSql.equals("SELECT * FROM table WHERE id = 123 AND name = test");
        
        // 测试UUID生成
        String uuid = SQLHelper.generateUUID();
        log.info("生成的UUID: {}", uuid);
        assert uuid != null && !uuid.isEmpty();
        
        log.info("静态方法测试完成");
    }
    
    @Test
    public void testCreateSqlHelperWithoutSpringContext() {
        log.info("测试在没有完整Spring上下文的情况下创建SQLHelper...");
        
        try {
            // 这个测试可能会失败，因为SQLHelperImpl依赖Spring上下文
            // 但我们可以捕获异常并验证错误信息
            SQLHelper sqlHelper = SQLHelper.createSqlHelper("Sys_DataKFGL");
            log.info("SQLHelper 创建成功: {}", sqlHelper);
        } catch (Exception e) {
            log.warn("SQLHelper 创建失败（这是预期的）: {}", e.getMessage());
            // 验证是否是预期的Spring上下文错误
            assert e.getMessage().contains("Spring") || e.getCause() != null;
        }
        
        log.info("测试完成");
    }
}
