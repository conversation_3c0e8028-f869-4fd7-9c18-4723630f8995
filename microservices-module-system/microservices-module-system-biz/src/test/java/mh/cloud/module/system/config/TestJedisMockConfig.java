package mh.cloud.module.system.config;

import com.github.fppt.jedismock.RedisServer;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.core.annotation.Order;

import java.io.IOException;
import java.net.ServerSocket;

/**
 * 测试环境的Redis Mock配置
 * 使用动态端口避免冲突
 */
@TestConfiguration
@Order(1)
@Slf4j
public class TestJedisMockConfig {
    
    private RedisServer redisServer;
    private int port;
    
    @PostConstruct
    public void init() throws Exception {
        // 查找可用端口
        port = findAvailablePort();
        log.info("启动测试Redis服务器，端口: {}", port);
        
        redisServer = new RedisServer(port);
        redisServer.start();
        
        // 设置系统属性，让Spring Data Redis使用这个端口
        System.setProperty("spring.data.redis.port", String.valueOf(port));
    }
    
    @PreDestroy
    public void stopRedisServer() throws IOException {
        if (redisServer != null) {
            log.info("停止测试Redis服务器");
            redisServer.stop();
        }
    }
    
    /**
     * 查找可用端口
     */
    private int findAvailablePort() {
        // 从6392开始查找可用端口
        for (int port = 6392; port < 6500; port++) {
            if (isPortAvailable(port)) {
                return port;
            }
        }
        throw new RuntimeException("无法找到可用的端口");
    }
    
    /**
     * 检查端口是否可用
     */
    private boolean isPortAvailable(int port) {
        try (ServerSocket serverSocket = new ServerSocket(port)) {
            return true;
        } catch (IOException e) {
            return false;
        }
    }
    
    public int getPort() {
        return port;
    }
}
