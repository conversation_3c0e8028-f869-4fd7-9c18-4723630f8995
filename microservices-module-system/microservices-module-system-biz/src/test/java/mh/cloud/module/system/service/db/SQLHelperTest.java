package mh.cloud.module.system.service.db;

import lombok.extern.slf4j.Slf4j;
import mh.cloud.module.system.SystemServerApplication;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;

/**
 * SQLHelper 测试类
 * 
 * @Author: ytq
 * @Date: 2025/7/29 11:00
 * @Description: SQLHelper 功能测试
 */
@SpringBootTest(classes = SystemServerApplication.class)
@ActiveProfiles("local") // 使用本地配置文件
@Slf4j
public class SQLHelperTest {

    @Test
    public void testCreateSqlHelper() {
        try {
            // 测试创建 SQLHelper 实例
            SQLHelper sqlHelper = SQLHelper.createSqlHelper("Sys_DataKFGL");
            log.info("SQLHelper 创建成功: {}", sqlHelper);
            
            // 如果需要测试数据库连接，可以执行一个简单的查询
            // 注意：这里需要确保数据库配置正确且数据库可访问
            // List<Map<String, Object>> result = sqlHelper.executeReader("SELECT 1 as test");
            // log.info("查询结果: {}", result);
            
        } catch (Exception e) {
            log.error("测试失败", e);
            throw e;
        }
    }
    
    @Test
    public void testSqlHelperMethods() {
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Sys_DataKFGL")) {
            log.info("SQLHelper 实例创建成功");
            
            // 测试格式化方法
            String formattedSql = SQLHelper.format("SELECT * FROM table WHERE id = {0} AND name = {1}", "123", "test");
            log.info("格式化SQL: {}", formattedSql);
            
            // 测试UUID生成
            String uuid = SQLHelper.generateUUID();
            log.info("生成的UUID: {}", uuid);
            
        } catch (Exception e) {
            log.error("测试失败", e);
            throw e;
        }
    }
}
