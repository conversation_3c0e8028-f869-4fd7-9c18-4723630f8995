package mh.cloud.module.system.service.db;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.HashMap;
import java.util.Map;

/**
 * SQLHelper 独立测试类（不依赖Spring上下文）
 * 
 * @Author: ytq
 * @Date: 2025/7/29 11:00
 * @Description: 测试SQLHelper的基本功能，不需要Spring环境
 */
@Slf4j
public class SQLHelperStandaloneTest {

    @Test
    public void testStaticMethods() {
        // 测试静态方法，这些不需要Spring上下文
        
        // 测试格式化方法
        String formattedSql = SQLHelper.format("SELECT * FROM table WHERE id = {0} AND name = {1}", "123", "test");
        log.info("格式化SQL: {}", formattedSql);
        assert formattedSql.equals("SELECT * FROM table WHERE id = 123 AND name = test");
        
        // 测试UUID生成
        String uuid = SQLHelper.generateUUID();
        log.info("生成的UUID: {}", uuid);
        assert uuid != null && !uuid.isEmpty();
        
        // 测试字符串格式化方法
        Map<String, Object> userDic = new HashMap<>();
        userDic.put("ID", "user123");
        userDic.put("Name", "张三");
        
        Map<String, Object> dic = new HashMap<>();
        dic.put("test", "测试值");
        
        String result = SQLHelper.formatString("用户ID: {UserID}, 用户名: {UserName}, 测试: {test}", userDic, dic);
        log.info("格式化字符串结果: {}", result);
    }
    
    @Test
    public void testConnectionCreation() {
        // 这个测试演示如何直接创建SQLHelper实例（如果有有效的数据库连接）
        // 注意：这个测试可能会失败，因为需要实际的数据库连接
        
        try {
            // 示例：如果你有一个测试数据库连接
            // String jdbcUrl = "jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE";
            // String username = "sa";
            // String password = "";
            // Class.forName("org.h2.Driver");
            // Connection connection = DriverManager.getConnection(jdbcUrl, username, password);
            // SQLHelperImpl sqlHelper = new SQLHelperImpl(connection);
            // log.info("SQLHelper 创建成功: {}", sqlHelper);
            
            log.info("连接创建测试跳过 - 需要实际的数据库配置");
            
        } catch (Exception e) {
            log.warn("连接创建测试失败（这是预期的，因为没有配置测试数据库）: {}", e.getMessage());
        }
    }
}
