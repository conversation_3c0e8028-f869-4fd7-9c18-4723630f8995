spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h
  data:
    redis:
      host: 127.0.0.1
      port: 6392  # 使用不同的端口避免冲突
      database: 0
      repositories:
        enabled: false

# 禁用健康检查
management:
  health:
    redis:
      enabled: false

# 微服务配置
microservices:
  captcha:
    enable: false
  security:
    mock-enable: true
  xss:
    enable: false
  demo: false

# 日志配置
logging:
  level:
    mh.cloud.module.system: DEBUG
    com.github.fppt.jedismock: WARN
