# 客户端IP记录配置
microservices:
  web:
    client-ip:
      # 是否启用客户端IP记录
      enable: true
      # 是否记录详细信息
      detailed: true
      # 需要详细记录的URI模式
      detailed-patterns:
        - "/admin-api/system/auth/"
        - "/app-api/member/auth/"
        - "/admin-api/system/oauth2/"
        - "/admin-api/system/captcha/"
        - "/admin-api/system/portal/"
      # 需要跳过记录的URI模式  
      skip-patterns:
        - "/admin-api/infra/file/"
        - "/favicon.ico"
        - "/actuator/"
        - "/swagger-ui/"
        - "/v3/api-docs"
        - "/webjars/"
        - "/error"

# 日志配置
logging:
  level:
    # 客户端IP相关日志级别
    mh.cloud.framework.common.util.servlet.ClientIPUtils: INFO
    mh.cloud.framework.web.core.interceptor.ClientIPInterceptor: INFO
    # 认证相关日志级别
    mh.cloud.module.system.service.auth: INFO
    mh.cloud.module.system.controller.admin.auth: INFO
