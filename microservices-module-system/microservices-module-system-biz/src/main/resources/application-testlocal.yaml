spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
    web-application-type: none  # 设置为非Web应用
  # 禁用可能导致问题的自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      - com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration
      - org.springframework.boot.autoconfigure.security.oauth2.client.servlet.OAuth2ClientAutoConfiguration
      - org.springframework.boot.autoconfigure.security.oauth2.resource.servlet.OAuth2ResourceServerAutoConfiguration
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration
      - org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration
      - org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration
      - org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration
      - org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration
      - de.codecentric.boot.admin.client.config.SpringBootAdminClientAutoConfiguration
  # Cache 配置项
  cache:
    type: none  # 禁用缓存
  data:
    redis:
      repositories:
        enabled: false

# 禁用健康检查
management:
  health:
    redis:
      enabled: false
    db:
      enabled: false
  endpoints:
    enabled-by-default: false

# 微服务配置
microservices:
  captcha:
    enable: false
  security:
    mock-enable: true
  xss:
    enable: false
  demo: false
  tenant:
    enable: false

# 日志配置
logging:
  level:
    root: WARN
    mh.cloud.module.system: DEBUG
    com.github.fppt.jedismock: WARN
    org.springframework: WARN
    com.baomidou.mybatisplus: WARN
    org.springframework.boot.autoconfigure: WARN
    javax.net.ssl: DEBUG  # 启用SSL调试日志

# 服务器配置
server:
  port: 0  # 使用随机端口
