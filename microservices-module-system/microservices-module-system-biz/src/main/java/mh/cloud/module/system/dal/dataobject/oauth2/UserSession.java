package mh.cloud.module.system.dal.dataobject.oauth2;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

public class UserSession {
    private String uuid;
    private String userId;
    private boolean loginStatus;
    private LocalDateTime loginTime; // 新增的登录时间字段
    private Map<String, Object> userInfo;

    public Map<String, Object> getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(Map<String, Object> userInfo) {
        this.userInfo = userInfo;
    }

    // Getters and Setters
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public boolean isLoginStatus() {
        return loginStatus;
    }

    public void setLoginStatus(boolean loginStatus) {
        this.loginStatus = loginStatus;
    }

    public LocalDateTime getLoginTime() {
        return loginTime; // 获取登录时间
    }

    public void setLoginTime(LocalDateTime loginTime) {
        this.loginTime = loginTime; // 设置登录时间
    }
}
