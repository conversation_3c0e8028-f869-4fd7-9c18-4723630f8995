package mh.cloud.module.system.dal.mysql.oauth2;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.mybatis.core.mapper.BaseMapperX;
import mh.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import mh.cloud.framework.tenant.core.aop.TenantIgnore;
import mh.cloud.module.system.controller.admin.oauth2.vo.token.OAuth2AccessTokenPageReqVO;
import mh.cloud.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface OAuth2AccessTokenMapper extends BaseMapperX<OAuth2AccessTokenDO> {

    @TenantIgnore // 获取 token 的时候，需要忽略租户编号。原因是：一些场景下，可能不会传递 tenant-id 请求头，例如说文件上传、积木报表等等
    default OAuth2AccessTokenDO selectByAccessToken(String accessToken) {

        OAuth2AccessTokenDO oAuth2AccessTokenDO = selectOne(OAuth2AccessTokenDO::getAccessToken, accessToken);
        if (ObjUtil.isEmpty(oAuth2AccessTokenDO)) {
            return null;
        }
        return oAuth2AccessTokenDO;
    }

    default List<OAuth2AccessTokenDO> selectListByRefreshToken(String refreshToken) {
        return selectList(OAuth2AccessTokenDO::getRefreshToken, refreshToken);
    }

    default PageResult<OAuth2AccessTokenDO> selectPage(OAuth2AccessTokenPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<OAuth2AccessTokenDO>()
                .eqIfPresent(OAuth2AccessTokenDO::getUserId, reqVO.getUserId())
                .eqIfPresent(OAuth2AccessTokenDO::getUserType, reqVO.getUserType())
                .likeIfPresent(OAuth2AccessTokenDO::getClientId, reqVO.getClientId())
                .gt(OAuth2AccessTokenDO::getExpiresTime, LocalDateTime.now())
                .orderByDesc(OAuth2AccessTokenDO::getId));
    }

    default OAuth2AccessTokenDO getTokenById(String UserId) {
        LambdaQueryWrapper<OAuth2AccessTokenDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OAuth2AccessTokenDO::getUserId, UserId)
                .gt(OAuth2AccessTokenDO::getExpiresTime, new DateTime())
                .orderByDesc(OAuth2AccessTokenDO::getExpiresTime) // 按过期时间降序
                .last("LIMIT 1");
        return selectOne(queryWrapper);
    }

    /**
     * 不需要逻辑删除 直接删除token
     */
    @Delete("DELETE FROM system_oauth2_access_token WHERE id = #{id}")
    void deleteByIdNoDel(Long id);


    @Delete("DELETE FROM system_oauth2_access_token WHERE deleted = 1 AND user_id = #{userId}")
    void clearToken(String userId);

    @Delete("DELETE FROM system_oauth2_access_token WHERE expires_time < CURRENT_TIMESTAMP AND user_id = #{userId}")
    void clearExpiredTokenByUserId(String userId);
}
