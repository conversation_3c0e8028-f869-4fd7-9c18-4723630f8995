package mh.cloud.module.system.job.core.service;

import mh.cloud.module.system.dal.dataobject.job.TaskOptions;

import java.time.LocalDateTime;

/**
 * Job 日志 Framework Service 接口
*/
public interface JobLogFrameworkService {

    /**
     * 创建 Job 日志
     *
     * @param jobId           任务编号
     * @param beginTime       开始时间
     * @return Job 日志的编号
     */
    String createJobLog(String jobId, LocalDateTime beginTime, TaskOptions options);

    /**
     * 更新 Job 日志的执行结果
     *
     * @param logId    日志编号
     * @param endTime  结束时间。因为是异步，避免记录时间不准去
     * @param duration 运行时长，单位：毫秒
     * @param success  是否成功
     * @param result   成功数据
     * @param jobId
     */
    void updateJobLogResultAsync(String logId, LocalDateTime endTime, Integer duration, boolean success, String result, String jobId);
}
