package mh.cloud.module.system.util;

import lombok.extern.slf4j.Slf4j;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.SSLSocketFactory;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.Arrays;

/**
 * TLS验证工具
 * 用于验证TLS配置是否正确应用
 */
@Slf4j
public class TLSVerificationTool {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("TLS配置验证工具");
        System.out.println("========================================");
        
        try {
            // 1. 检查系统属性
            System.out.println("\n[步骤1] 检查系统属性...");
            checkSystemProperties();
            
            // 2. 检查安全属性
            System.out.println("\n[步骤2] 检查安全属性...");
            checkSecurityProperties();
            
            // 3. 检查SSL上下文
            System.out.println("\n[步骤3] 检查SSL上下文...");
            checkSSLContext();
            
            // 4. 测试SQL Server连接字符串
            System.out.println("\n[步骤4] 测试SQL Server连接字符串构建...");
            testSQLServerConnectionString();
            
            System.out.println("\n========================================");
            System.out.println("TLS配置验证完成！");
            System.out.println("========================================");
            
        } catch (Exception e) {
            System.err.println("验证过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 检查系统属性
     */
    private static void checkSystemProperties() {
        String[] properties = {
            "https.protocols",
            "jdk.tls.client.protocols",
            "jdk.tls.disabledAlgorithms",
            "jdk.certpath.disabledAlgorithms",
            "com.microsoft.sqlserver.jdbc.fips",
            "com.microsoft.sqlserver.jdbc.trustServerCertificate",
            "com.microsoft.sqlserver.jdbc.encrypt",
            "com.sun.net.ssl.checkRevocation",
            "jsse.enableSNIExtension"
        };
        
        for (String property : properties) {
            String value = System.getProperty(property);
            System.out.println("  " + property + " = " + (value != null ? value : "未设置"));
        }
    }
    
    /**
     * 检查安全属性
     */
    private static void checkSecurityProperties() {
        String[] properties = {
            "jdk.tls.disabledAlgorithms",
            "jdk.certpath.disabledAlgorithms"
        };
        
        for (String property : properties) {
            String value = java.security.Security.getProperty(property);
            System.out.println("  Security." + property + " = " + (value != null ? value : "未设置"));
        }
    }
    
    /**
     * 检查SSL上下文
     */
    private static void checkSSLContext() {
        try {
            SSLContext context = SSLContext.getDefault();
            SSLSocketFactory factory = context.getSocketFactory();
            
            try (SSLSocket socket = (SSLSocket) factory.createSocket()) {
                String[] supportedProtocols = socket.getSupportedProtocols();
                String[] enabledProtocols = socket.getEnabledProtocols();
                
                System.out.println("  支持的TLS协议: " + Arrays.toString(supportedProtocols));
                System.out.println("  启用的TLS协议: " + Arrays.toString(enabledProtocols));
                
                boolean supportsTLS10 = Arrays.asList(supportedProtocols).contains("TLSv1");
                boolean enablesTLS10 = Arrays.asList(enabledProtocols).contains("TLSv1");
                
                System.out.println("  TLS 1.0 支持: " + (supportsTLS10 ? "✓" : "✗"));
                System.out.println("  TLS 1.0 启用: " + (enablesTLS10 ? "✓" : "✗"));
            }
            
        } catch (Exception e) {
            System.err.println("  检查SSL上下文时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 测试SQL Server连接字符串构建
     */
    private static void testSQLServerConnectionString() {
        try {
            // 构建测试连接字符串
            String testUrl = "**********************************************************************************************************************;";
            
            System.out.println("  测试连接字符串: " + testUrl);
            
            // 检查SQL Server驱动
            try {
                Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                System.out.println("  ✓ SQL Server JDBC驱动加载成功");
            } catch (ClassNotFoundException e) {
                System.out.println("  ✗ SQL Server JDBC驱动未找到: " + e.getMessage());
                return;
            }
            
            // 验证连接字符串格式
            if (testUrl.contains("encrypt=false") && testUrl.contains("trustServerCertificate=true")) {
                System.out.println("  ✓ 连接字符串包含SSL禁用参数");
            } else {
                System.out.println("  ✗ 连接字符串缺少SSL禁用参数");
            }
            
        } catch (Exception e) {
            System.err.println("  测试连接字符串时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 测试实际数据库连接（需要提供真实的连接参数）
     */
    public static boolean testActualConnection(String jdbcUrl, String username, String password) {
        try {
            System.out.println("测试实际数据库连接...");
            System.out.println("JDBC URL: " + jdbcUrl);
            
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            
            try (Connection connection = DriverManager.getConnection(jdbcUrl, username, password)) {
                System.out.println("✓ 数据库连接成功！");
                return true;
            }
            
        } catch (Exception e) {
            System.err.println("✗ 数据库连接失败: " + e.getMessage());
            return false;
        }
    }
}
