package mh.cloud.module.system.util;

import mh.cloud.module.system.service.db.SqlServiceImpl;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.SSLSocketFactory;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;

/**
 * SSL/TLS 测试工具
 * 用于测试和验证SSL配置
 */
public class SSLTestTool {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("SSL/TLS 配置测试工具");
        System.out.println("========================================");
        
        try {
            // 1. 配置SSL设置
            System.out.println("\n[步骤1] 配置SSL/TLS设置...");
            SqlServiceImpl.configureSSLForDatabase();
            
            // 2. 检查支持的TLS协议
            System.out.println("\n[步骤2] 检查支持的TLS协议...");
            checkSupportedTLSProtocols();
            
            // 3. 显示当前SSL系统属性
            System.out.println("\n[步骤3] 当前SSL系统属性:");
            displaySSLProperties();
            
            // 4. 测试SSL上下文
            System.out.println("\n[步骤4] 测试SSL上下文...");
            testSSLContext();
            
            System.out.println("\n========================================");
            System.out.println("SSL/TLS 配置测试完成！");
            System.out.println("========================================");
            
        } catch (Exception e) {
            System.err.println("SSL/TLS 测试失败:");
            e.printStackTrace();
        }
    }
    
    /**
     * 检查支持的TLS协议
     */
    private static void checkSupportedTLSProtocols() {
        try {
            SSLContext context = SSLContext.getDefault();
            SSLSocketFactory factory = context.getSocketFactory();
            
            // 创建一个SSL Socket来检查支持的协议
            try (SSLSocket socket = (SSLSocket) factory.createSocket()) {
                String[] supportedProtocols = socket.getSupportedProtocols();
                String[] enabledProtocols = socket.getEnabledProtocols();
                
                System.out.println("支持的协议:");
                for (String protocol : supportedProtocols) {
                    System.out.println("  - " + protocol);
                }
                
                System.out.println("启用的协议:");
                for (String protocol : enabledProtocols) {
                    System.out.println("  - " + protocol);
                }
            }
            
        } catch (Exception e) {
            System.err.println("检查TLS协议失败: " + e.getMessage());
        }
    }
    
    /**
     * 显示SSL相关的系统属性
     */
    private static void displaySSLProperties() {
        String[] sslProperties = {
            "https.protocols",
            "jdk.tls.client.protocols",
            "com.sun.net.ssl.checkRevocation",
            "com.microsoft.sqlserver.jdbc.fips",
            "com.microsoft.sqlserver.jdbc.trustServerCertificate",
            "javax.net.debug"
        };
        
        for (String property : sslProperties) {
            String value = System.getProperty(property);
            System.out.println("  " + property + " = " + (value != null ? value : "未设置"));
        }
    }
    
    /**
     * 测试SSL上下文
     */
    private static void testSSLContext() {
        try {
            // 测试不同的SSL上下文
            String[] contexts = {"TLS", "TLSv1", "TLSv1.1", "TLSv1.2", "TLSv1.3"};
            
            for (String contextName : contexts) {
                try {
                    SSLContext context = SSLContext.getInstance(contextName);
                    System.out.println("  ✓ " + contextName + " - 支持");
                } catch (NoSuchAlgorithmException e) {
                    System.out.println("  ✗ " + contextName + " - 不支持");
                }
            }
            
        } catch (Exception e) {
            System.err.println("测试SSL上下文失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试到指定主机的SSL连接
     */
    public static boolean testSSLConnection(String host, int port) {
        try {
            System.out.println("测试SSL连接到 " + host + ":" + port);
            
            SSLSocketFactory factory = (SSLSocketFactory) SSLSocketFactory.getDefault();
            try (SSLSocket socket = (SSLSocket) factory.createSocket(host, port)) {
                socket.startHandshake();
                System.out.println("  ✓ SSL连接成功");
                return true;
            }
            
        } catch (IOException e) {
            System.out.println("  ✗ SSL连接失败: " + e.getMessage());
            return false;
        }
    }
}
