package mh.cloud.module.system.util;

import cn.hutool.core.util.ObjUtil;
import mh.cloud.module.system.service.db.SQLHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Component
public class PatternMatchUtil {

    private static final String enable = "web.enable";
    private static final String prefix = "web.config.";

    private final Environment environment;

    private final String settingSql = "select ParamValue from C_SystemParameter where Code='{0}' and (IsDeleted<>'1' or IsDeleted is null)";

    @Autowired
    public PatternMatchUtil(Environment environment) {
        this.environment = environment;
    }
    public String GetSettingValue(String key) {
        if (ObjUtil.isEmpty(key)) {
            return "";
        }
        if (Objects.equals(environment.getProperty(enable), "true")) {
            String property = environment.getProperty(prefix + key);
            if (!ObjUtil.isEmpty(property)) {
                return property;
            }
        }
        //若配置文件获取不到数据，从数据库中查询
        try (SQLHelper core = SQLHelper.CreateSqlHelper("Core")) {
            Map<String, Object> map = core.selectFirstRow(SQLHelper.format(settingSql, key));
            if (ObjUtil.isEmpty(map)) {
                return "";
            }
            return map.get("ParamValue").toString();
        }
    }

//    public static void main(String[] args) {
//        String content = "<a href='/BasicApplication/KindEditor/DownloadFile'>Link</a>";
//        String content = "<a href=\"http://10.10.1.173:8001/PageOffice/Preview/PreviewPageOfficePDF.aspx?OptnType=YNTZ&ID=b19d00f1-f128-4e51-9024-e5f82e82fd31\">点击此处打开正文内容</a>";
//        String hrefPattern = "<a\\b[^<>]*?\\bhref\\s*=?\\s*['\"]?\\s*(?<imgUrl>[^\\s'\"><>]*)[^<>]*?/?\\s*>";
//        content = replaceHref(content, hrefPattern);
//        System.out.println(content);

//        String content = "<img src='/BasicApplication/KindEditor/DownloadFile'>";
//        String pattern = "<img\\b[^<>]*?\\bsrc\\s*=?\\s*['\"]?\\s*(?<imgUrl>[^\\s'\"><>]*)[^<>]*?/?\\s*>";
//        content = updateImageUrls(content);
//        System.out.println(content);
//    }

    public String replaceHref(String content, String hrefPattern, String token) {
        Pattern pattern = Pattern.compile(hrefPattern, Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
        Matcher matcher = pattern.matcher(content);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String newValue = "";
            String oldImgUrl = matcher.group("imgUrl");
            if (!StringUtils.isEmpty(oldImgUrl)) {
                String finalUrl = "";
                boolean imageUrlIsHttp = getLinkDomain(oldImgUrl);
                if (imageUrlIsHttp && !oldImgUrl.contains("/PageOffice/ViewPDF.aspx") && !oldImgUrl.contains("/PageOffice/ViewWord.aspx") && !oldImgUrl.contains("/PageOffice/Preview/PreviewPageOfficePDF.aspx")) {
                    if (oldImgUrl.toLowerCase().contains("fileid") && (oldImgUrl.contains(".pdf") || oldImgUrl.contains(".docx") || oldImgUrl.contains(".xlsx"))) {
                        finalUrl = getIdUrl(oldImgUrl, token);
                    } else {
                        finalUrl = oldImgUrl;
                    }
                } else {
                    if (oldImgUrl.contains("/BasicApplication/KindEditor/DownloadFile")) {
                        if (oldImgUrl.contains("fileID") && (oldImgUrl.contains(".pdf") || oldImgUrl.contains(".docx") || oldImgUrl.contains(".xlsx"))) {
                            finalUrl = getIdUrl(oldImgUrl, token);
                        } else {
                            finalUrl =  GetSettingValue("downloadFileUrl") + oldImgUrl;
                        }
                    } else if (oldImgUrl.contains("/PageOffice/ViewPDF.aspx") || oldImgUrl.contains("/PageOffice/ViewWord.aspx") || oldImgUrl.contains("/PageOffice/Preview/PreviewPageOfficePDF.aspx")) {
                        if (oldImgUrl.contains("fileID")) {
                            finalUrl = getIdUrl(oldImgUrl, token);
                        } else {
                            finalUrl = "javascript:POBrowser.openWindowModeless('" + oldImgUrl + "','fullscreen=yes')";
                        }
                    } else {
                        if (!oldImgUrl.contains("data:image")) {
                            finalUrl = GetSettingValue("imageUrl") + oldImgUrl;
                        } else {
                            finalUrl = oldImgUrl;
                        }
                    }
                }
                if (oldImgUrl.contains("/PageOffice/ViewPDF.aspx") || oldImgUrl.contains("/PageOffice/ViewWord.aspx")) {
                    newValue = matcher.group().replace(oldImgUrl, finalUrl).replace("target=_Blank", "");
                } else {
                    newValue = matcher.group().replace(oldImgUrl, finalUrl);
                }
            }
//            else if (oldImgUrl.contains("/PageOffice/ViewPDF.aspx") || oldImgUrl.contains("/PageOffice/ViewWord.aspx") || oldImgUrl.contains("/PageOffice/Preview/PreviewPageOfficePDF.aspx")) {
//                finalUrl = "javascript:POBrowser.openWindowModeless('" + oldImgUrl + "','fullscreen=yes')";
//            }
//            if (newValue.contains("javascript:POBrowser.openWindowModeless")) {
//                newValue = newValue.replace("href=", "class=");
//            }
            matcher.appendReplacement(sb, newValue);
        }
        matcher.appendTail(sb);

        return repalceStr(sb.toString());
    }

    private String getIdUrl(String oldImgUrl, String token) {
        String idUrl = oldImgUrl.trim().substring(oldImgUrl.toLowerCase().indexOf("fileid"));
        idUrl = idUrl.substring(idUrl.indexOf("=")+1);
        if (idUrl.contains("&")) {
            idUrl = idUrl.substring(0, idUrl.indexOf("&"));
        }
        return GetSettingValue("downloadFileUrl") + "/PageOffice/Preview/PreviewPage?fileID=" + idUrl + "&token=" + token;
    }

    private String repalceStr(String str) {
        return str.replace(GetSettingValue("downloadFileUrlOld"), GetSettingValue("downloadFileUrl")).replace("\"", "`");
    }

    private static boolean getLinkDomain(String oldImgUrl) {
        return oldImgUrl.startsWith("http");
    }

    private String processMatch(Matcher matcher) {
        String newValue = "";
        String oldImgUrl = matcher.group("imgUrl");

        if (oldImgUrl != null && !oldImgUrl.trim().isEmpty()) {
            String finalUrl = null;
            boolean imageUrlIsHttp = oldImgUrl.startsWith("http");

            if (imageUrlIsHttp && !oldImgUrl.contains("/PageOffice/ViewPDF.aspx") && !oldImgUrl.contains("/PageOffice/ViewWord.aspx") && !oldImgUrl.contains("/PageOffice/Preview/PreviewPageOfficePDF.aspx")) {
                finalUrl = oldImgUrl;
            } else {
                if (oldImgUrl.contains("/BasicApplication/KindEditor/DownloadFile")) {
                    finalUrl = GetSettingValue("downloadFileUrl") + oldImgUrl;
                } else if (oldImgUrl.contains("/PageOffice/ViewPDF.aspx") || oldImgUrl.contains("/PageOffice/ViewWord.aspx") || oldImgUrl.contains("/PageOffice/Preview/PreviewPageOfficePDF.aspx")) {
                    finalUrl = "javascript:POBrowser.openWindowModeless('" + oldImgUrl + "','fullscreen=yes')";
                } else {
                    if (!oldImgUrl.contains("data:image")) {
                        finalUrl = GetSettingValue("imageUrl") + oldImgUrl;
                    } else {
                        finalUrl = oldImgUrl;
                    }
                }
            }

            if (oldImgUrl.contains("/PageOffice/ViewPDF.aspx") || oldImgUrl.contains("/PageOffice/ViewWord.aspx")) {
                newValue = matcher.group().replace(oldImgUrl, finalUrl).replace("target=_Blank", "");
            } else {
                newValue = matcher.group().replace(oldImgUrl, finalUrl);
            }
        }

        return newValue;
    }

    public String replaceImgSrc(String content, String pattern) {
        Pattern regex = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
        Matcher matcher = regex.matcher(content);

        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String newValue = "";
            String oldImgUrl = matcher.group("imgUrl");
            if (oldImgUrl != null && !oldImgUrl.isEmpty()) {
                String finalUrl = null;
                boolean imageUrlIsHttp = getLinkDomain(oldImgUrl);
                if (imageUrlIsHttp) {
                    finalUrl = oldImgUrl;
                } else {
                    if (oldImgUrl.contains("/BasicApplication/KindEditor/DownloadFile")) {
                        finalUrl = GetSettingValue("downloadFileUrl") + oldImgUrl;
                    } else {
                        if (!oldImgUrl.contains("data:image")) {
                            finalUrl = GetSettingValue("imageUrl") + oldImgUrl;
                        } else {
                            finalUrl = oldImgUrl;
                        }
                    }
                }
                newValue = matcher.group().replace(oldImgUrl, finalUrl);
            }
            matcher.appendReplacement(sb, newValue);
        }
        matcher.appendTail(sb);
        return repalceStr(sb.toString());
    }

    public String updateImageUrls(String content) {
        String pattern = "<img\\b[^<>]*?\\bsrc[\\s\\t\\r\\n]*=[\\s\\t\\r\\n]*[\"']?[\\s\\t\\r\\n]*(?<imgUrl>[^\\s\\t\\r\\n\"'<>]*)[^<>]*?/?[\\s\\t\\r\\n]*>";

        Pattern regexPattern = Pattern.compile(pattern);
        Matcher matcher = regexPattern.matcher(content);

        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String oldImgUrl = matcher.group("imgUrl");
            String newValue = matcher.group(0);

            if (oldImgUrl != null && !oldImgUrl.isEmpty()) {
                String finalUrl = null;
                String imageUrlIsHttp = getLinkDomain2(oldImgUrl);

                if (imageUrlIsHttp != null && !imageUrlIsHttp.trim().isEmpty()) {
                    if (oldImgUrl.contains("/KindEditor/Uploadimage/")) {
                        String substring = oldImgUrl.substring(oldImgUrl.lastIndexOf(":") + 1);
                        String path = substring.substring(substring.indexOf("/"));
                        finalUrl = GetSettingValue("downloadFileUrl") + "/BasicApplication/KindEditor/DownloadFile?path=" + path;
                    } else {
                        finalUrl = oldImgUrl;
                    }
//                    finalUrl = oldImgUrl;
                } else {
                    if (oldImgUrl.contains("/BasicApplication/KindEditor/DownloadFile")) {
                        finalUrl = GetSettingValue("downloadFileUrl") + oldImgUrl;
                    } else {
                        if (!oldImgUrl.contains("data:image")) {
                            finalUrl = GetSettingValue("imageUrl") + oldImgUrl;
                        } else {
                            finalUrl = oldImgUrl;
                        }
                    }
                }

                newValue = newValue.replace(oldImgUrl, finalUrl);

                if (!newValue.contains("height") && !newValue.contains("width") && !newValue.contains("style")) {
                    newValue = newValue.replace("/>", "style = 'max-width:700px' />");
                }
                newValue = newValue.replace("``", "' '").replace("`", "");
                //在img标签外添加<p>标签，处理部分图片不居中问题
                newValue = "<p>" + newValue + "</p>";
            }

            matcher.appendReplacement(result, newValue);
        }

        matcher.appendTail(result);

        return result.toString();
    }

    private static String getLinkDomain2(String url) {
        // Implement this function to check if the URL starts with HTTP/HTTPS or not
        if (url.startsWith("http://") || url.startsWith("https://") || url.startsWith("`http://") || url.startsWith("`https://")) {
            return url;
        }
        return null;
    }
}
