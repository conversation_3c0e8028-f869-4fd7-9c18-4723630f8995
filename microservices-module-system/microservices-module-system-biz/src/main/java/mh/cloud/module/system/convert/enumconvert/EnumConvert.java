package mh.cloud.module.system.convert.enumconvert;

import mh.cloud.framework.common.util.collection.CollectionUtils;
import mh.cloud.module.system.controller.admin.enumcontroller.vo.EnumDefVO;
import mh.cloud.module.system.controller.admin.enumcontroller.vo.EnumItemVO;
import mh.cloud.module.system.dal.dataobject.enumdo.EnumDefDO;
import mh.cloud.module.system.dal.dataobject.enumdo.EnumItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper
public interface EnumConvert {
    EnumConvert INSTANCE = Mappers.getMapper(EnumConvert.class);

    @Mappings({
            @Mapping(target = "id", source = "id"),
            @Mapping(target = "code", source = "code"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "type", source = "type")
    })
    EnumDefVO DefConvert(EnumDefDO bean);

    @Mappings({
            @Mapping(target = "id", source = "id"),
            @Mapping(target = "enumDefId", source = "enumDefId"),
            @Mapping(target = "code", source = "code"),
            @Mapping(target = "name", source = "name")
    })
    EnumItemVO ItemConvert(EnumItemDO bean);

    default List<EnumDefVO> convertDefList(List<EnumDefDO> list) {
        return CollectionUtils.convertList(list, this::DefConvert);
    }

    default List<EnumItemVO> convertItemList(List<EnumItemDO> list) {
        return CollectionUtils.convertList(list, this::ItemConvert);
    }
}
