package mh.cloud.module.system;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;

import java.io.IOException;

/**
 * 项目的启动类
 */
@SpringBootApplication
@EnableFeignClients
@EnableAsync
public class SystemServerApplication extends SpringBootServletInitializer {

    // 静态初始化块，在类加载时就执行TLS配置
    static {
        configureTLSForSQLServerStatic();
    }
    /**
     * 静态TLS配置方法，在类加载时执行
     */
    private static void configureTLSForSQLServerStatic() {
        try {
            // 强制设置系统属性
            System.setProperty("https.protocols", "TLSv1,TLSv1.1,TLSv1.2,TLSv1.3");
            System.setProperty("jdk.tls.client.protocols", "TLSv1,TLSv1.1,TLSv1.2,TLSv1.3");
            System.setProperty("jdk.tls.disabledAlgorithms", "");
            System.setProperty("jdk.certpath.disabledAlgorithms", "");

            // 强制设置安全属性
            java.security.Security.setProperty("jdk.tls.disabledAlgorithms", "");
            java.security.Security.setProperty("jdk.certpath.disabledAlgorithms", "");

            // SQL Server特定配置
            System.setProperty("com.microsoft.sqlserver.jdbc.fips", "false");
            System.setProperty("com.microsoft.sqlserver.jdbc.trustServerCertificate", "true");
            System.setProperty("com.microsoft.sqlserver.jdbc.encrypt", "false");
            System.setProperty("com.sun.net.ssl.checkRevocation", "false");
            System.setProperty("jsse.enableSNIExtension", "false");
        } catch (Exception e) {
            System.err.println("静态TLS配置失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    // 重写 configure 方法，支持打包成 war 包
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(SystemServerApplication.class);
    }

    public static void main(String[] args) throws IOException {
        SpringApplication.run(SystemServerApplication.class, args);
        System.out.println("=== System 启动成功 (^_^) ===");
    }

}
