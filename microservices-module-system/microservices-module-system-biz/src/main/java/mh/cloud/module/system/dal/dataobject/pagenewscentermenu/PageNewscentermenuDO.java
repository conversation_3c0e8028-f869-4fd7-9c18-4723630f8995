package mh.cloud.module.system.dal.dataobject.pagenewscentermenu;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import mh.cloud.framework.mybatis.core.dataobject.BaseDO;

/**
 * 菜单管理 DO
 *
 * <AUTHOR>
 */
@TableName("T_Page_NewsCenterMenu")
@KeySequence("t_page_newscentermenu_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageNewscentermenuDO {

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 创建人
     */
    @TableField(value = "CreateUser")
    private String createUser;
    /**
     * 创建人ID
     */
    @TableField(value = "CreateUserID")
    private String createUserID;
    /**
     * 创建日期
     */
    @TableField(value = "CreateTime")
    private LocalDateTime createTime;
    /**
     * 修改人
     */
    @TableField(value = "ModifyUser")
    private String modifyUser;
    /**
     * 修改人ID
     */
    @TableField(value = "ModifyUserID")
    private String modifyUserID;
    /**
     * 修改日期
     */
    @TableField(value = "ModifyTime")
    private LocalDateTime modifyTime;
    /**
     * 流程状态
     */
    @TableField(value = "FlowPhase")
    private String flowPhase;
    /**
     * 流程步骤
     */
    @TableField(value = "FlowStep")
    private String flowStep;
    /**
     * 流程结束日期
     */
    @TableField(value = "FlowCompleteTime")
    private LocalDateTime flowCompleteTime;
    /**
     * 是否删除
     */
    @TableField(value = "IsDeleted")
    private String isDeleted;
    /**
     * 状态
     */
    @TableField(value = "Status")
    private String status;
    /**
     * 所有签字、意见
     */
    @TableField(value = "SignTemp")
    private String signTemp;
    /**
     * 部门ID
     */
    @TableField(value = "DeptID")
    private String deptID;
    /**
     * 部门
     */
    @TableField(value = "DeptName")
    private String deptName;
    /**
     * 备注
     */
    @TableField(value = "Description")
    private String description;
    /**
     * 经手人
     */
    @TableField(value = "FlowHandler")
    private String flowHandler;
    /**
     * 经手人ID
     */
    @TableField(value = "FlowHandlerID")
    private String flowHandlerID;
    /**
     * 是否当前版本
     */
    @TableField(value = "IsCurrent")
    private String isCurrent;
    /**
     * 版本号
     */
    @TableField(value = "VersionNumber")
    private Integer versionNumber;
    /**
     * 修改记录大字段
     */
    @TableField(value = "ModifyRecordText")
    private String modifyRecordText;
    /**
     * 资讯中心菜单名称
     */
    @TableField(value = "CatalogName")
    private String catalogName;
    /**
     * 排序号
     */
    @TableField(value = "SortIndex")
    private String sortIndex;
    /**
     * 是否启用
     */
    @TableField(value = "IsEnabled")
    private String isEnabled;
    /**
     * 设置组织
     */
    @TableField(value = "DepartmentInfo")
    private String departmentInfo;
    /**
     * 设置组织名称
     */
    @TableField(value = "DepartmentInfoName")
    private String departmentInfoName;
    /**
     * 设置平台角色
     */
    @TableField(value = "RoleInfo")
    private String roleInfo;
    /**
     * 设置平台角色名称
     */
    @TableField(value = "RoleInfoName")
    private String roleInfoName;
    /**
     * 设置新建角色
     */
    @TableField(value = "SelfBuildRoleInfo")
    private String selfBuildRoleInfo;
    /**
     * 设置新建角色名称
     */
    @TableField(value = "SelfBuildRoleInfoName")
    private String selfBuildRoleInfoName;
    /**
     * 获取列表数据
     */
    @TableField(value = "ListSql")
    private String listSql;
    /**
     * 获取数据详情
     */
    @TableField(value = "DetailSql")
    private String detailSql;
    /**
     * 编码
     */
    @TableField(value = "Code")
    private String code;
    /**
     * 序号
     */
    @TableField(value = "OrderNumber")
    private String orderNumber;
    /**
     * 浏览次数
     */
    @TableField(value = "ViewCountSql")
    private String viewCountSql;
    /**
     * 资讯中心菜单名称
     */
    @TableField(value = "MenuName")
    private String menuName;
    @TableField(value = "tenant_id")
    private String tenantId;

}