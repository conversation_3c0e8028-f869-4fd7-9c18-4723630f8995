package mh.cloud.module.system.util;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

public class SymmetricEncryptionUtils {
    private static final String ALGORITHM = "AES";
    private static final int KEY_SIZE = 256;
    public static final String KEY = "aKqoyO28C8HAMuUecFjxqZB1MoSRbLNTnphAz5gRo4w=";


    /**
     * 获取秘钥
     */
    public static String generateKey() throws NoSuchAlgorithmException {
        KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
        SecureRandom secureRandom = new SecureRandom();
        keyGenerator.init(KEY_SIZE, secureRandom);
        Secret<PERSON>ey secretKey = keyGenerator.generateKey();
        return keyBytesToString(secretKey.getEncoded());
    }

    public static String keyBytesToString(byte[] keyBytes) {
        return Base64.getEncoder().encodeToString(keyBytes);
    }

    public static byte[] stringToKeyBytes(String keyStr) {
        return Base64.getDecoder().decode(keyStr);
    }

    /**
     * 加密
     */
    public static String encrypt(String data, String key) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(stringToKeyBytes(key), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] byteData = data.getBytes(StandardCharsets.UTF_8);
        byte[] encryptedBytes = cipher.doFinal(byteData);
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * 解密
     */
    public static String decrypt(String encryptedData, String key) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(stringToKeyBytes(key), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        byte[] byteEncryptedData = Base64.getDecoder().decode(encryptedData);
        byte[] decryptedBytes = cipher.doFinal(byteEncryptedData);
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }


//    r/hpGdAmJ1mlSqdHROrgEwc4NR6fju1MoejfMuOmNy8=
//    aKqoyO28C8HAMuUecFjxqZB1MoSRbLNTnphAz5gRo4w=

    public static void main(String[] args) throws Exception {
        String string = "aKqoyO28C8HAMuUecFjxqZB1MoSRbLNTnphAz5gRo4w=";
        System.out.println(string);

        String data = "USER2A7Y";

        String encrypt = encrypt(data, string);
        System.out.println("密文：" + encrypt);
        String decrypt = decrypt(encrypt, string);

        System.out.println("明文：" + decrypt);

    }

}
