package mh.cloud.module.system.config;

import com.github.fppt.jedismock.RedisServer;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import java.io.IOException;

@Configuration("jedisMockConfig")
@Order(1)
public class JedisMockConfig {
    private final RedisServer redisServer = new RedisServer(6391);
    @PostConstruct
    public void init() throws Exception {
        redisServer.start();
    }
    @PreDestroy
    public void stopRedisServer() throws IOException {
        if (redisServer != null) {
            redisServer.stop();
        }
    }
}
