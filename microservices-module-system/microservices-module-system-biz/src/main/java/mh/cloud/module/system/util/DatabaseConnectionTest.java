package mh.cloud.module.system.util;

import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * 数据库连接测试工具
 * 用于测试SQL Server连接和用户认证
 */
@Slf4j
public class DatabaseConnectionTest {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("数据库连接测试工具");
        System.out.println("========================================");
        
        // 测试SQL Server连接
        testSQLServerConnection();
    }
    
    /**
     * 测试SQL Server连接
     */
    private static void testSQLServerConnection() {
        System.out.println("\n[测试] SQL Server连接...");
        
        // 连接参数
        String host = "**********";
        int port = 1433;
        String database = "kmyzh_systemdatabase";
        String[] possibleUsers = {"kddbuse", "kdbbuse", "sa", "admin"};
        String[] possiblePasswords = {"Khidi@1836!@#$Ksf", "123456", "admin", ""};
        
        // 不同的连接字符串配置
        String[] urlConfigs = {
            // 完全禁用SSL
            "*********************************************************************************************************;",
            // 启用SSL但信任证书
            "********************************************************************************************************;",
            // 最简配置
            "**************************************;"
        };
        
        try {
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            System.out.println("✓ SQL Server JDBC驱动加载成功");
        } catch (ClassNotFoundException e) {
            System.err.println("✗ SQL Server JDBC驱动未找到: " + e.getMessage());
            return;
        }
        
        boolean connectionSuccessful = false;
        
        // 测试不同的配置组合
        for (int configIndex = 0; configIndex < urlConfigs.length; configIndex++) {
            String urlTemplate = urlConfigs[configIndex];
            String url = String.format(urlTemplate, host, port, database);
            
            System.out.println("\n配置 " + (configIndex + 1) + ": " + url);
            
            for (String user : possibleUsers) {
                for (String password : possiblePasswords) {
                    try {
                        System.out.print("  尝试: " + user + "/" + (password.isEmpty() ? "(空密码)" : "***") + " ... ");
                        
                        Connection connection = DriverManager.getConnection(url, user, password);
                        
                        System.out.println("✓ 连接成功！");
                        System.out.println("    成功的配置:");
                        System.out.println("    URL: " + url);
                        System.out.println("    用户名: " + user);
                        System.out.println("    密码: " + (password.isEmpty() ? "(空)" : "***"));
                        
                        // 测试简单查询
                        try {
                            var stmt = connection.createStatement();
                            var rs = stmt.executeQuery("SELECT 1 as test");
                            if (rs.next()) {
                                System.out.println("    ✓ 查询测试成功");
                            }
                            rs.close();
                            stmt.close();
                        } catch (Exception e) {
                            System.out.println("    ✗ 查询测试失败: " + e.getMessage());
                        }
                        
                        connection.close();
                        connectionSuccessful = true;
                        
                        // 找到成功的配置后，可以选择继续测试其他配置或停止
                        // return; // 取消注释这行来在第一次成功后停止测试
                        
                    } catch (SQLException e) {
                        System.out.println("✗ " + e.getMessage());
                    }
                }
            }
        }
        
        if (!connectionSuccessful) {
            System.out.println("\n所有连接尝试都失败了。请检查:");
            System.out.println("1. SQL Server是否正在运行");
            System.out.println("2. 网络连接是否正常");
            System.out.println("3. 用户名和密码是否正确");
            System.out.println("4. 数据库名称是否正确");
            System.out.println("5. SQL Server是否允许远程连接");
            System.out.println("6. 防火墙设置");
        }
    }
    
    /**
     * 测试特定的连接配置
     */
    public static boolean testSpecificConnection(String url, String username, String password) {
        try {
            System.out.println("测试连接: " + url);
            System.out.println("用户名: " + username);
            
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            
            Connection connection = DriverManager.getConnection(url, username, password);
            System.out.println("✓ 连接成功");
            
            // 测试查询
            var stmt = connection.createStatement();
            var rs = stmt.executeQuery("SELECT GETDATE() as current_time");
            if (rs.next()) {
                System.out.println("✓ 查询成功，当前时间: " + rs.getString("current_time"));
            }
            
            rs.close();
            stmt.close();
            connection.close();
            
            return true;
            
        } catch (Exception e) {
            System.err.println("✗ 连接失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取推荐的连接字符串
     */
    public static String getRecommendedConnectionString(String host, int port, String database) {
        return String.format(
            "*********************************************************************************************************;",
            host, port, database
        );
    }
}
