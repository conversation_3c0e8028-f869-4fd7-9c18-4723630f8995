package mh.cloud.module.system.config;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.cert.X509Certificate;

/**
 * SSL配置类，用于处理SSL/TLS版本兼容性问题
 */
@Configuration
@Slf4j
public class SSLConfig {

    @PostConstruct
    public void configureSSL() {
        try {
            log.info("配置SSL设置以支持旧版本TLS（包括SQL Server TLS 1.0）...");

            // 设置支持的TLS协议版本（包括TLS 1.0以支持旧版SQL Server）
            System.setProperty("https.protocols", "TLSv1,TLSv1.1,TLSv1.2,TLSv1.3");
            System.setProperty("jdk.tls.client.protocols", "TLSv1,TLSv1.1,TLSv1.2,TLSv1.3");

            // 清空禁用的算法列表以启用TLS 1.0
            System.setProperty("jdk.tls.disabledAlgorithms", "");
            System.setProperty("jdk.certpath.disabledAlgorithms", "");

            // SQL Server特定的SSL配置
            configureSQLServerSSL();

            // 禁用证书撤销检查（仅用于测试环境）
            System.setProperty("com.sun.net.ssl.checkRevocation", "false");

            // 创建信任所有证书的TrustManager（仅用于测试环境）
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
            };

            // 安装信任所有证书的TrustManager
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            SSLContext.setDefault(sc);

            // 设置主机名验证器（仅用于测试环境）
            HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);

            log.info("SSL配置完成，已启用TLS 1.0-1.3支持");

        } catch (Exception e) {
            log.error("SSL配置失败", e);
        }
    }

    /**
     * 配置SQL Server特定的SSL设置
     */
    private void configureSQLServerSSL() {
        try {
            // SQL Server JDBC驱动特定配置
            System.setProperty("com.microsoft.sqlserver.jdbc.fips", "false");
            System.setProperty("com.microsoft.sqlserver.jdbc.trustServerCertificate", "true");
            System.setProperty("com.microsoft.sqlserver.jdbc.encrypt", "false");

            // 禁用SNI扩展（某些旧版SQL Server可能需要）
            System.setProperty("jsse.enableSNIExtension", "false");

            log.debug("SQL Server SSL配置完成");

        } catch (Exception e) {
            log.warn("SQL Server SSL配置失败: {}", e.getMessage());
        }
    }
}
