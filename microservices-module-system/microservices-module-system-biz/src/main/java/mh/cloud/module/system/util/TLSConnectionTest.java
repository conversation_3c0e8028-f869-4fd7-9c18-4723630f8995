package mh.cloud.module.system.util;

import lombok.extern.slf4j.Slf4j;
import mh.cloud.module.system.service.db.SqlServiceImpl;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.SSLSocketFactory;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.Arrays;

/**
 * TLS连接测试工具
 * 用于测试和验证TLS配置是否正确
 */
@Slf4j
public class TLSConnectionTest {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("TLS连接测试工具");
        System.out.println("========================================");
        
        try {
            // 1. 配置SSL/TLS设置
            System.out.println("\n[步骤1] 配置SSL/TLS设置...");
            SqlServiceImpl.configureSSLForDatabase();
            
            // 2. 检查支持的TLS协议
            System.out.println("\n[步骤2] 检查支持的TLS协议...");
            checkSupportedTLSProtocols();
            
            // 3. 显示当前SSL系统属性
            System.out.println("\n[步骤3] 当前SSL系统属性:");
            displaySSLProperties();
            
            // 4. 测试SQL Server连接字符串
            System.out.println("\n[步骤4] 测试SQL Server连接字符串...");
            testSQLServerConnectionString();
            
            System.out.println("\n========================================");
            System.out.println("TLS连接测试完成！");
            System.out.println("========================================");
            
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 检查支持的TLS协议
     */
    private static void checkSupportedTLSProtocols() {
        try {
            SSLContext context = SSLContext.getDefault();
            SSLSocketFactory factory = context.getSocketFactory();
            
            // 创建一个临时的SSL Socket来获取支持的协议
            try (SSLSocket socket = (SSLSocket) factory.createSocket()) {
                String[] supportedProtocols = socket.getSupportedProtocols();
                String[] enabledProtocols = socket.getEnabledProtocols();
                
                System.out.println("  支持的TLS协议: " + Arrays.toString(supportedProtocols));
                System.out.println("  启用的TLS协议: " + Arrays.toString(enabledProtocols));
                
                // 检查是否支持TLS 1.0
                boolean supportsTLS10 = Arrays.asList(supportedProtocols).contains("TLSv1");
                boolean enablesTLS10 = Arrays.asList(enabledProtocols).contains("TLSv1");
                
                System.out.println("  TLS 1.0 支持: " + (supportsTLS10 ? "✓" : "✗"));
                System.out.println("  TLS 1.0 启用: " + (enablesTLS10 ? "✓" : "✗"));
            }
            
        } catch (Exception e) {
            System.err.println("  检查TLS协议时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 显示SSL相关的系统属性
     */
    private static void displaySSLProperties() {
        String[] sslProperties = {
            "https.protocols",
            "jdk.tls.client.protocols", 
            "jdk.tls.disabledAlgorithms",
            "jdk.certpath.disabledAlgorithms",
            "com.microsoft.sqlserver.jdbc.fips",
            "com.microsoft.sqlserver.jdbc.trustServerCertificate",
            "com.microsoft.sqlserver.jdbc.encrypt",
            "com.sun.net.ssl.checkRevocation",
            "jsse.enableSNIExtension"
        };
        
        for (String property : sslProperties) {
            String value = System.getProperty(property);
            System.out.println("  " + property + " = " + (value != null ? value : "未设置"));
        }
    }
    
    /**
     * 测试SQL Server连接字符串构建
     */
    private static void testSQLServerConnectionString() {
        try {
            // 构建一个测试用的SQL Server连接字符串
            String testConnectionString = "*******************************;" +
                    "databaseName=TestDB;" +
                    "encrypt=false;" +
                    "trustServerCertificate=true;" +
                    "loginTimeout=30;" +
                    "sslProtocol=TLS;" +
                    "enabledTLSProtocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3;" +
                    "hostNameInCertificate=*;" +
                    "integratedSecurity=false;" +
                    "sendStringParametersAsUnicode=false;" +
                    "applicationIntent=ReadWrite;" +
                    "multiSubnetFailover=false";
            
            System.out.println("  测试连接字符串:");
            System.out.println("  " + testConnectionString);
            
            // 尝试加载SQL Server驱动
            try {
                Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                System.out.println("  ✓ SQL Server JDBC驱动加载成功");
            } catch (ClassNotFoundException e) {
                System.out.println("  ✗ SQL Server JDBC驱动未找到: " + e.getMessage());
                return;
            }
            
            // 注意：这里不实际连接数据库，只是验证连接字符串格式
            System.out.println("  ✓ 连接字符串格式验证通过");
            
        } catch (Exception e) {
            System.err.println("  测试SQL Server连接字符串时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 测试到指定主机的SSL连接（可选）
     */
    public static boolean testSSLConnection(String host, int port) {
        try {
            System.out.println("测试SSL连接到 " + host + ":" + port);
            
            SSLSocketFactory factory = (SSLSocketFactory) SSLSocketFactory.getDefault();
            try (SSLSocket socket = (SSLSocket) factory.createSocket(host, port)) {
                // 设置启用的协议
                socket.setEnabledProtocols(new String[]{"TLSv1", "TLSv1.1", "TLSv1.2", "TLSv1.3"});
                socket.startHandshake();
                System.out.println("  ✓ SSL连接成功");
                return true;
            }
            
        } catch (IOException e) {
            System.out.println("  ✗ SSL连接失败: " + e.getMessage());
            return false;
        }
    }
}
