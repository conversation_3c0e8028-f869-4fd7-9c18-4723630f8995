package mh.cloud.module.system.util;

import lombok.extern.slf4j.Slf4j;
import mh.cloud.module.system.service.db.SQLHelper;

/**
 * 最简单的SQLHelper测试
 * 只测试静态方法，不依赖Spring上下文
 */
@Slf4j
public class SimpleTest {
    
    public static void main(String[] args) {
        System.out.println("=== 开始简单测试 ===");
        
        try {
            // 测试1: UUID生成
            System.out.println("测试1: UUID生成");
            String uuid = SQLHelper.generateUUID();
            System.out.println("生成的UUID: " + uuid);
            
            // 测试2: SQL格式化
            System.out.println("测试2: SQL格式化");
            String sql = SQLHelper.format("SELECT * FROM table WHERE id = {0}", "123");
            System.out.println("格式化的SQL: " + sql);
            
            // 测试3: 尝试创建SQLHelper（这个可能会失败）
            System.out.println("测试3: 尝试创建SQLHelper");
            try {
                SQLHelper helper = SQLHelper.createSqlHelper("test");
                System.out.println("SQLHelper创建成功: " + helper);
            } catch (Exception e) {
                System.out.println("SQLHelper创建失败（这是预期的）: " + e.getMessage());
            }
            
            System.out.println("=== 测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("=== 测试失败 ===");
            System.err.println("错误: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
}
