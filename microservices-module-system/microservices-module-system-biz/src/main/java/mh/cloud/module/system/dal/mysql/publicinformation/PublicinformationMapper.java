package mh.cloud.module.system.dal.mysql.publicinformation;

import java.util.*;

import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import mh.cloud.framework.mybatis.core.mapper.BaseMapperX;
import mh.cloud.module.system.dal.dataobject.publicinformation.PublicinformationDO;
import org.apache.ibatis.annotations.Mapper;
import mh.cloud.module.system.controller.admin.publicinformation.vo.*;

/**
 * 院内信息发布 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PublicinformationMapper extends BaseMapperX<PublicinformationDO> {

    default PageResult<PublicinformationDO> selectPage(PublicinformationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PublicinformationDO>()
                .eqIfPresent(PublicinformationDO::getCatalogId, reqVO.getCatalogId())
                .eqIfPresent(PublicinformationDO::getTitle, reqVO.getTitle())
                .eqIfPresent(PublicinformationDO::getContent, reqVO.getContent())
                .eqIfPresent(PublicinformationDO::getContentText, reqVO.getContentText())
                .eqIfPresent(PublicinformationDO::getAttachments, reqVO.getAttachments())
                .eqIfPresent(PublicinformationDO::getReceiveDeptId, reqVO.getReceiveDeptId())
                .likeIfPresent(PublicinformationDO::getReceiveDeptName, reqVO.getReceiveDeptName())
                .eqIfPresent(PublicinformationDO::getReceiveUserId, reqVO.getReceiveUserId())
                .likeIfPresent(PublicinformationDO::getReceiveUserName, reqVO.getReceiveUserName())
                .eqIfPresent(PublicinformationDO::getDeptDoorId, reqVO.getDeptDoorId())
                .likeIfPresent(PublicinformationDO::getDeptDoorName, reqVO.getDeptDoorName())
                .betweenIfPresent(PublicinformationDO::getExpiresTime, reqVO.getExpiresTime())
                .eqIfPresent(PublicinformationDO::getReadCount, reqVO.getReadCount())
                .eqIfPresent(PublicinformationDO::getImportant, reqVO.getImportant())
                .eqIfPresent(PublicinformationDO::getUrgency, reqVO.getUrgency())
                .eqIfPresent(PublicinformationDO::getIsTop, reqVO.getIsTop())
                .betweenIfPresent(PublicinformationDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(PublicinformationDO::getCreateUser, reqVO.getCreateUser())
                .eqIfPresent(PublicinformationDO::getCreateUserID, reqVO.getCreateUserID())
                .eqIfPresent(PublicinformationDO::getModifyUser, reqVO.getModifyUser())
                .eqIfPresent(PublicinformationDO::getModifyUserID, reqVO.getModifyUserID())
                .betweenIfPresent(PublicinformationDO::getModifyTime, reqVO.getModifyTime())
                .eqIfPresent(PublicinformationDO::getSystemCode, reqVO.getSystemCode())
                .eqIfPresent(PublicinformationDO::getIsSendMobile, reqVO.getIsSendMobile())
                .eqIfPresent(PublicinformationDO::getPicFile, reqVO.getPicFile())
                .eqIfPresent(PublicinformationDO::getFlowPhase, reqVO.getFlowPhase())
                .eqIfPresent(PublicinformationDO::getFlowStep, reqVO.getFlowStep())
                .betweenIfPresent(PublicinformationDO::getFlowCompleteTime, reqVO.getFlowCompleteTime())
                .eqIfPresent(PublicinformationDO::getFlowHandler, reqVO.getFlowHandler())
                .eqIfPresent(PublicinformationDO::getFlowHandlerID, reqVO.getFlowHandlerID())
                .likeIfPresent(PublicinformationDO::getCatalogName, reqVO.getCatalogName())
                .eqIfPresent(PublicinformationDO::getPublishDept, reqVO.getPublishDept())
                .likeIfPresent(PublicinformationDO::getPublishDeptName, reqVO.getPublishDeptName())
                .eqIfPresent(PublicinformationDO::getPublishState, reqVO.getPublishState())
                .eqIfPresent(PublicinformationDO::getDeputyTitle, reqVO.getDeputyTitle())
                .eqIfPresent(PublicinformationDO::getAbStract, reqVO.getAbStract())
                .eqIfPresent(PublicinformationDO::getNewsType, reqVO.getNewsType())
                .eqIfPresent(PublicinformationDO::getIsExtranet, reqVO.getIsExtranet())
                .eqIfPresent(PublicinformationDO::getEditorCharge, reqVO.getEditorCharge())
                .eqIfPresent(PublicinformationDO::getIsImage, reqVO.getIsImage())
                .eqIfPresent(PublicinformationDO::getReleaseScope, reqVO.getReleaseScope())
                .eqIfPresent(PublicinformationDO::getWordsAuthor, reqVO.getWordsAuthor())
                .eqIfPresent(PublicinformationDO::getImageAuthor, reqVO.getImageAuthor())
                .betweenIfPresent(PublicinformationDO::getReleaseDate, reqVO.getReleaseDate())
                .eqIfPresent(PublicinformationDO::getSource, reqVO.getSource())
                .eqIfPresent(PublicinformationDO::getKeyWords, reqVO.getKeyWords())
                .likeIfPresent(PublicinformationDO::getReleaseScopeName, reqVO.getReleaseScopeName())
                .eqIfPresent(PublicinformationDO::getIsPic, reqVO.getIsPic())
                .eqIfPresent(PublicinformationDO::getExtranetType, reqVO.getExtranetType())
                .eqIfPresent(PublicinformationDO::getReleaseCompany, reqVO.getReleaseCompany())
                .eqIfPresent(PublicinformationDO::getEnteredBy, reqVO.getEnteredBy())
                .eqIfPresent(PublicinformationDO::getVideFile, reqVO.getVideFile())
                .likeIfPresent(PublicinformationDO::getReleaseCompanyName, reqVO.getReleaseCompanyName())
                .likeIfPresent(PublicinformationDO::getEnteredByName, reqVO.getEnteredByName())
                .likeIfPresent(PublicinformationDO::getCatalogIDName, reqVO.getCatalogIDName())
                .eqIfPresent(PublicinformationDO::getMgccx, reqVO.getMgccx())
                .eqIfPresent(PublicinformationDO::getPicFileMINI, reqVO.getPicFileMINI())
                .eqIfPresent(PublicinformationDO::getTopNum, reqVO.getTopNum())
                .eqIfPresent(PublicinformationDO::getWordsAuthor2, reqVO.getWordsAuthor2())
                .eqIfPresent(PublicinformationDO::getWordsAuthor3, reqVO.getWordsAuthor3())
                .eqIfPresent(PublicinformationDO::getPublishUser, reqVO.getPublishUser())
                .likeIfPresent(PublicinformationDO::getPublishUserName, reqVO.getPublishUserName())
                .eqIfPresent(PublicinformationDO::getPublishUnit, reqVO.getPublishUnit())
                .likeIfPresent(PublicinformationDO::getPublishUnitName, reqVO.getPublishUnitName())
                .eqIfPresent(PublicinformationDO::getIsdelete, reqVO.getIsdelete())
                .eqIfPresent(PublicinformationDO::getCjrq, reqVO.getCjrq())
                .eqIfPresent(PublicinformationDO::getOtherWebAddres, reqVO.getOtherWebAddres())
                .likeIfPresent(PublicinformationDO::getAuthorName, reqVO.getAuthorName())
                .betweenIfPresent(PublicinformationDO::getSendTime, reqVO.getSendTime())
                .eqIfPresent(PublicinformationDO::getXxlx, reqVO.getXxlx())
                .eqIfPresent(PublicinformationDO::getFirstImageUrl, reqVO.getFirstImageUrl())
                .eqIfPresent(PublicinformationDO::getAuthor, reqVO.getAuthor())
                .likeIfPresent(PublicinformationDO::getWordsAuthor2Name, reqVO.getWordsAuthor2Name())
                .likeIfPresent(PublicinformationDO::getWordsAuthor3Name, reqVO.getWordsAuthor3Name())
                .likeIfPresent(PublicinformationDO::getImageAuthorName, reqVO.getImageAuthorName())
                .likeIfPresent(PublicinformationDO::getAuthorNameName, reqVO.getAuthorNameName())
                .eqIfPresent(PublicinformationDO::getBigFl, reqVO.getBigFl())
                .betweenIfPresent(PublicinformationDO::getIsTopEndTime, reqVO.getIsTopEndTime())
                .eqIfPresent(PublicinformationDO::getOutKey, reqVO.getOutKey())
                .eqIfPresent(PublicinformationDO::getOutType, reqVO.getOutType())
                .eqIfPresent(PublicinformationDO::getFirstFile, reqVO.getFirstFile())
                .eqIfPresent(PublicinformationDO::getEndFile, reqVO.getEndFile())
                .eqIfPresent(PublicinformationDO::getLk, reqVO.getLk())
                .eqIfPresent(PublicinformationDO::getContentZW, reqVO.getContentZW())
                .betweenIfPresent(PublicinformationDO::getWJDate, reqVO.getWJDate())
                .eqIfPresent(PublicinformationDO::getIsXZ, reqVO.getIsXZ())
                .orderByDesc(PublicinformationDO::getId));
    }

}