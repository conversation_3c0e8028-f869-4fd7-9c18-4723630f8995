package mh.cloud.module.system.util;

import lombok.extern.slf4j.Slf4j;
import mh.cloud.module.system.SystemServerApplication;
import mh.cloud.module.system.service.db.SQLHelper;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * @Author: ytq
 * @Date: 2025/7/29 11:00
 * @Description: SQLHelper 测试启动类
 */
@SpringBootApplication
@ComponentScan(basePackages = "mh.cloud.module.system")
@Slf4j
public class test implements CommandLineRunner {

    public static void main(String[] args) {
        // 设置Spring配置文件
        System.setProperty("spring.profiles.active", "local");
        // 禁用一些可能导致端口冲突的组件
        System.setProperty("server.port", "0"); // 使用随机端口
        System.setProperty("management.server.port", "0");

        SpringApplication app = new SpringApplication(test.class);
        // 设置为非Web应用
        app.setWebApplicationType(org.springframework.boot.WebApplicationType.NONE);
        app.run(args);
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("=== SQLHelper 测试开始 ===");

        try {
            // 首先测试静态方法（不依赖Spring上下文）
            log.info("1. 测试静态方法...");
            testStaticMethods();

            // 然后测试需要Spring上下文的方法
            log.info("2. 测试SQLHelper创建...");
            testSQLHelperCreation();

            log.info("=== 所有测试完成 ===");

        } catch (Exception e) {
            log.error("=== 测试失败 ===", e);
            log.error("异常类型: {}", e.getClass().getName());
            log.error("异常消息: {}", e.getMessage());
            if (e.getCause() != null) {
                log.error("根本原因: {}", e.getCause().getMessage());
            }

            // 打印完整的堆栈跟踪
            e.printStackTrace();

            // 确保程序正常退出而不是崩溃
            System.exit(1);
        }
    }

    private void testStaticMethods() {
        log.info("测试UUID生成...");
        String uuid = SQLHelper.generateUUID();
        log.info("生成的UUID: {}", uuid);

        log.info("测试SQL格式化...");
        String formattedSql = SQLHelper.format("SELECT * FROM table WHERE id = {0} AND name = {1}", "123", "test");
        log.info("格式化SQL: {}", formattedSql);

        log.info("静态方法测试完成");
    }

    private void testSQLHelperCreation() {
        log.info("尝试创建SQLHelper实例...");
        try {
            SQLHelper oa = SQLHelper.createSqlHelper("Sys_DataKFGL");
            log.info("SQLHelper 创建成功: {}", oa);

            // 如果创建成功，尝试一些基本操作
            if (oa != null) {
                log.info("SQLHelper实例类型: {}", oa.getClass().getName());
            }

        } catch (Exception e) {
            log.error("SQLHelper创建失败", e);
            throw e;
        }
    }
}
