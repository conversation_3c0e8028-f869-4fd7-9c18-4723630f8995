package mh.cloud.module.system.util;


import cn.hutool.core.util.ObjUtil;
import mh.cloud.module.system.service.db.SQLHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
public class ConfigurationHelper {


    private static final String enable = "web.enable";
    private static final String prefix = "web.config.";

    private final Environment environment;

    private final String settingSql = "select ParamValue from C_SystemParameter where Code='{0}' and (IsDeleted<>'1' or IsDeleted is null)";

    @Autowired
    public ConfigurationHelper(Environment environment) {
        this.environment = environment;
    }


    public String GetSettingValue(String key) {
        if (ObjUtil.isEmpty(key)) {
            return "";
        }
        if (Objects.equals(environment.getProperty(enable), "true")) {
            String property = environment.getProperty(prefix + key);
            if (!ObjUtil.isEmpty(property)) {
                return property;
            }
        }
        //若配置文件获取不到数据，从数据库中查询
        try (SQLHelper core = SQLHelper.CreateSqlHelper("Core")) {
            Map<String, Object> map = core.selectFirstRow(SQLHelper.format(settingSql, key));
            if (ObjUtil.isEmpty(map)) {
                return "";
            }
            return map.get("ParamValue").toString();
        }
    }
}
