package mh.cloud.module.system.dal.dataobject.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("A_UserExts")
public class AUserExtsDO {
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
    @TableField(value = "UserID")
    private String userID;
    @TableField(value = "SignImg")
    private byte[] signImg;
    @TableField(value = "HeadPortrait")
    private byte[] headPortrait;
    @TableField(value = "UsedName")
    private String usedName;
    @TableField(value = "Nation")
    private String nation;
    @TableField(value = "NativePlace")
    private String nativePlace;
    @TableField(value = "HealthStatus")
    private String healthStatus;
    @TableField(value = "MaritalStatus")
    private String maritalStatus;
    @TableField(value = "WorkDate")
    private LocalDateTime workDate;
    @TableField(value = "Source")
    private String source;
    @TableField(value = "UserType")
    private String userType;
    @TableField(value = "WagesPaid")
    private String wagesPaid;
    @TableField(value = "SocialSecurity")
    private String socialSecurity;
    @TableField(value = "Accumulation")
    private String accumulation;
    @TableField(value = "Comprehensive")
    private String comprehensive;
    @TableField(value = "IDCard")
    private String idCard;
    @TableField(value = "PoliticalOutlook")
    private String politicalOutlook;
    @TableField(value = "OfficeAddress")
    private String officeAddress;
    @TableField(value = "tenant_id")
    private String tenantId;
    @TableField(value = "SignImgFile")
    private String signImgFile;
    @TableField(value = "HeadPortraitFile")
    private String headPortraitFile;
}