package mh.cloud.module.system.dal.dataobject.configmanage;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import mh.cloud.framework.mybatis.core.dataobject.BaseDO;

/**
 * 系统配置结构树 DO
 *
 * <AUTHOR>
 */
@TableName("a_configmanage")
@KeySequence("a_configmanage_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigmanageDO {

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 父ID
     */
    @TableField(value = "parentID")
    private String parentID;
    /**
     * 全ID
     */
    @TableField(value = "fullID")
    private String fullID;
    /**
     * 排序索引
     */
    @TableField(value = "sortIndex")
    private Integer sortIndex;
    /**
     * 名称
     */
    @TableField(value = "name")
    private String name;
    /**
     * code
     */
    @TableField(value = "code")
    private String code;
    /**
     * 节点类型
     */
    @TableField(value = "type")
    private String type;
    /**
     * iconcls
     */
    @TableField(value = "iconCls")
    private String iconCls;
    /**
     * 主界面地址
     */
    @TableField(value = "url")
    private String url;
    /**
     * 控制类型
     */
    @TableField(value = "ctrlType")
    private String ctrlType;
    /**
     * 权限，可以为页面控件ID，数据的查询条件
     */
    @TableField(value = "auth")
    private String auth;
    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;
    /**
     * 子系统编号
     */
    @TableField(value = "systemCode")
    private String systemCode;
    /**
     * 创建人
     */
    @TableField(value = "createUser")
    private String createUser;
    /**
     * 创建用户id
     */
    @TableField(value = "createUserID")
    private String createUserID;
    /**
     * 创建时间
     */
    @TableField(value = "createTime")
    private LocalDateTime createTime;
    /**
     * 修改用户
     */
    @TableField(value = "modifyUser")
    private String modifyUser;
    /**
     * 修改用户id
     */
    @TableField(value = "modifyUserID")
    private String modifyUserID;
    /**
     * 修改时间
     */
    @TableField(value = "modifyTime")
    private LocalDateTime modifyTime;
    /**
     * 节点状态（未发布，已发布）
     */
    @TableField(value = "status")
    private String status;
    /**
     * 节点编辑权限
     */
    @TableField(value = "editAuth")
    private String editAuth;
    /**
     * 节点编辑权限
     */
    @TableField(value = "editAuthUser")
    private String editAuthUser;
    /**
     * 节点的连接页面
     */
    @TableField(value = "configUrl")
    private String configUrl;
    /**
     * 主数据库连接
     */
    @TableField(value = "mainDBConn")
    private String mainDBConn;
    /**
     * relateId
     */
    @TableField(value = "relateID")
    private String relateID;
    /**
     * 关联表
     */
    @TableField(value = "relateTable")
    private String relateTable;
    /**
     * 是否主界面
     */
    @TableField(value = "isMainUrl")
    private String isMainUrl;
    /**
     * 是否删除
     */
    @TableField(value = "isDeleted")
    private String isDeleted;
    /**
     * isStandard
     */
    @TableField(value = "isStandard")
    private String isStandard;
    @TableField(value = "tenant_id")
    private String tenantId;

}