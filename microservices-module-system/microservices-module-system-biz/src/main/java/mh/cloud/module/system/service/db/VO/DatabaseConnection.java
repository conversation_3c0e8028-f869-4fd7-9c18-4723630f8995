package mh.cloud.module.system.service.db.VO;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class DatabaseConnection {


    public static Connection getTargetConnection() throws SQLException, ClassNotFoundException {
        String url = "***********************************************************************************************************";
        String user = "kddbuse";
        String password = "Khidi@1836!@#$Ksf";
        Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        return DriverManager.getConnection(url, user, password);
    }
}
