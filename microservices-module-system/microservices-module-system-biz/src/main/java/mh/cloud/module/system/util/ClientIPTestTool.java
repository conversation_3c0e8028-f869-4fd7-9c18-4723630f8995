package mh.cloud.module.system.util;

import mh.cloud.framework.common.util.servlet.ClientIPUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mock.web.MockHttpServletRequest;

/**
 * 客户端IP测试工具
 * 用于测试各种代理环境下的IP获取
 */
@Slf4j
public class ClientIPTestTool {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("客户端IP获取测试工具");
        System.out.println("========================================");
        
        // 测试各种代理场景
        testDirectAccess();
        testNginxProxy();
        testCloudflareProxy();
        testMultiLevelProxy();
        testInvalidIPs();
        
        System.out.println("========================================");
        System.out.println("测试完成！");
        System.out.println("========================================");
    }
    
    /**
     * 测试直接访问
     */
    private static void testDirectAccess() {
        System.out.println("\n[测试1] 直接访问（无代理）");
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRemoteAddr("*************");
        
        ClientIPUtils.ClientIPInfo info = ClientIPUtils.getClientIPInfo(request);
        System.out.println("结果: " + info);
    }
    
    /**
     * 测试Nginx代理
     */
    private static void testNginxProxy() {
        System.out.println("\n[测试2] Nginx代理");
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRemoteAddr("********"); // Nginx服务器IP
        request.addHeader("X-Real-IP", "*************"); // 真实客户端IP
        request.addHeader("X-Forwarded-For", "*************");
        
        ClientIPUtils.ClientIPInfo info = ClientIPUtils.getClientIPInfo(request);
        System.out.println("结果: " + info);
    }
    
    /**
     * 测试Cloudflare代理
     */
    private static void testCloudflareProxy() {
        System.out.println("\n[测试3] Cloudflare代理");
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRemoteAddr("**********"); // Cloudflare IP
        request.addHeader("CF-Connecting-IP", "**************");
        request.addHeader("X-Forwarded-For", "**************, **********");
        
        ClientIPUtils.ClientIPInfo info = ClientIPUtils.getClientIPInfo(request);
        System.out.println("结果: " + info);
    }
    
    /**
     * 测试多级代理
     */
    private static void testMultiLevelProxy() {
        System.out.println("\n[测试4] 多级代理");
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRemoteAddr("********");
        request.addHeader("X-Forwarded-For", "*************, **********, ***************");
        
        ClientIPUtils.ClientIPInfo info = ClientIPUtils.getClientIPInfo(request);
        System.out.println("结果: " + info);
    }
    
    /**
     * 测试无效IP
     */
    private static void testInvalidIPs() {
        System.out.println("\n[测试5] 无效IP处理");
        
        // 测试unknown
        MockHttpServletRequest request1 = new MockHttpServletRequest();
        request1.setRemoteAddr("127.0.0.1");
        request1.addHeader("X-Forwarded-For", "unknown");
        ClientIPUtils.ClientIPInfo info1 = ClientIPUtils.getClientIPInfo(request1);
        System.out.println("unknown IP结果: " + info1);
        
        // 测试空值
        MockHttpServletRequest request2 = new MockHttpServletRequest();
        request2.setRemoteAddr("127.0.0.1");
        request2.addHeader("X-Forwarded-For", "");
        ClientIPUtils.ClientIPInfo info2 = ClientIPUtils.getClientIPInfo(request2);
        System.out.println("空IP结果: " + info2);
        
        // 测试无效格式
        MockHttpServletRequest request3 = new MockHttpServletRequest();
        request3.setRemoteAddr("127.0.0.1");
        request3.addHeader("X-Forwarded-For", "invalid.ip.address");
        ClientIPUtils.ClientIPInfo info3 = ClientIPUtils.getClientIPInfo(request3);
        System.out.println("无效格式IP结果: " + info3);
    }
    
    /**
     * 测试真实环境的IP获取
     */
    public static void testRealEnvironment() {
        System.out.println("\n=== 真实环境IP获取测试 ===");
        
        // 模拟常见的生产环境配置
        String[][] testCases = {
            {"阿里云SLB", "X-Forwarded-For", "************, ********"},
            {"腾讯云CLB", "X-Real-IP", "*************"},
            {"AWS ELB", "X-Forwarded-For", "************, **********"},
            {"华为云ELB", "X-Forwarded-For", "*************, ***********"},
            {"百度云BLB", "X-Forwarded-For", "*************, ********"}
        };
        
        for (String[] testCase : testCases) {
            System.out.println("\n[" + testCase[0] + "]");
            MockHttpServletRequest request = new MockHttpServletRequest();
            request.setRemoteAddr("********");
            request.addHeader(testCase[1], testCase[2]);
            
            ClientIPUtils.ClientIPInfo info = ClientIPUtils.getClientIPInfo(request);
            System.out.println("结果: " + info);
        }
    }
    
    /**
     * 性能测试
     */
    public static void performanceTest() {
        System.out.println("\n=== 性能测试 ===");
        
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRemoteAddr("********");
        request.addHeader("X-Forwarded-For", "*************, **********, ***************");
        request.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        
        int iterations = 10000;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < iterations; i++) {
            ClientIPUtils.getClientIP(request);
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        System.out.println("执行 " + iterations + " 次IP获取");
        System.out.println("总耗时: " + totalTime + "ms");
        System.out.println("平均耗时: " + (totalTime * 1.0 / iterations) + "ms");
        System.out.println("每秒处理: " + (iterations * 1000.0 / totalTime) + " 次");
    }
}
