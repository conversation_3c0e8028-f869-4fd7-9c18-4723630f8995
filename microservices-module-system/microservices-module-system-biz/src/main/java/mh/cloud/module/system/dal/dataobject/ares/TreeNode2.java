package mh.cloud.module.system.dal.dataobject.ares;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class TreeNode2 {
    private String id;
    private String name;
    @JsonIgnore
    private String parentId;
    private List<TreeNode2> children;

    public TreeNode2(String id, String name, String parentId) {
        this.id = id;
        this.name = name;
        this.parentId = parentId;
    }

    @Override
    public String toString() {
        return '{' +
                "id:'" + id + '\'' +
                ", name:'" + name + '\'' +
                ", parentId:'" + parentId + '\'' +
                ", children:" + children +
                '}';
    }
}
