package mh.cloud.module.system.api.portal;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import mh.cloud.module.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME, url = "#{T(mh.cloud.module.system.enums.ApiConstants).url}")
@Tag(name = "RPC 服务 - 系统参数")
public interface SystemApi {
    String PREFIX = ApiConstants.PREFIX + "/Portal/WorkCenter";

    @GetMapping(PREFIX + "/executeSql")
    @Operation(summary = "通过用户 ID 查询用户")
    Object executeSql(@RequestParam("database") String database,@RequestParam("sql") String sql);

    @GetMapping(PREFIX + "/selectSql")
    @Operation(summary = "通过用户 ID 查询用户")
    List<Map<String,Object>> selectSql(@RequestParam("database") String database, @RequestParam("sql") String sql);



    @GetMapping(PREFIX + "/GetSettingValue")
    @Operation(summary = "查看配置数据")
    String getSettingValue(@RequestParam("key") String key);


}
