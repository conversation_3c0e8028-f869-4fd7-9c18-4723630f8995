spring:
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
  cloud:
    # Spring Cloud Gateway 配置项，对应 GatewayProperties 类
    gateway:
      isNacos: false # 是否使用 Nacos 作为注册中心
      # 路由配置项，对应 RouteDefinition 数组
      routes:
        - id: auth-api # 路由的编号
          #uri: grayLb://system-server
          uri: http://127.0.0.1:48090
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/auth/**
          filters:
            - RewritePath=/auth/login, /admin-api/auth/login # 配置，保证转发到 /v3/api-docs
            - RewritePath=/auth/cursessionOA, /admin-api/auth/cursessionOA # 配置，保证转发到 /v3/api-docs
            - RewritePath=/auth/getTokenById, /admin-api/auth/getTokenById # 配置，保证转发到 /v3/api-docs
        - id: admin-auth-api # 路由的编号
          #uri: grayLb://system-server
          uri: http://127.0.0.1:48090
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/auth/**
        - id: Portal # 路由的编号
          #uri: grayLb://system-server
          uri: http://127.0.0.1:48090
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/Portal/**
          filters:
            - RewritePath=/admin-api/system/v3/api-docs, /v3/api-docs # 配置，保证转发到 /v3/api-docs
        - id: BasicApplication # 路由的编号
          #uri: grayLb://system-server
          uri: http://127.0.0.1:48090
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/BasicApplication/**
          filters:
            - RewritePath=/admin-api/system/v3/api-docs, /v3/api-docs # 配置，保证转发到 /v3/api-docs
        ## system-server 服务
        - id: system-admin-api # 路由的编号
          #uri: grayLb://system-server
          uri: http://127.0.0.1:48090
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/system/**
          filters:
            - RewritePath=/admin-api/system/v3/api-docs, /v3/api-docs # 配置，保证转发到 /v3/api-docs
        ## system-server 服务
        - id: system-callback # 路由的编号
          #uri: grayLb://system-server
          uri: http://127.0.0.1:48090
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/callback/**
          filters:
            - RewritePath=/admin-api/system/v3/api-docs, /v3/api-docs # 配置，保证转发到 /v3/api-docs
        - id: system-app-api # 路由的编号
          #uri: grayLb://system-server
          uri: http://127.0.0.1:48090
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/app-api/system/**
          filters:
            - RewritePath=/app-api/system/v3/api-docs, /v3/api-docs
        ## infra-server 服务
        - id: infra-admin-api # 路由的编号
          #uri: grayLb://infra-server
          uri: http://127.0.0.1:48082
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/infra/**
          filters:
            - RewritePath=/admin-api/infra/v3/api-docs, /v3/api-docs
        - id: DownFile # 路由的编号
          #uri: grayLb://infra-server
          uri: http://127.0.0.1:48082
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/downFile/**
        - id: infra-app-api # 路由的编号
          #uri: grayLb://infra-server
          uri: http://127.0.0.1:48082
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/app-api/infra/**
          filters:
            - RewritePath=/app-api/infra/v3/api-docs, /v3/api-docs
        - id: infra-spring-boot-admin # 路由的编号（Spring Boot Admin）
          #uri: grayLb://infra-server
          uri: http://127.0.0.1:48082
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin/**
        - id: infra-websocket # 路由的编号（WebSocket）
          #uri: grayLb://infra-server
          uri: http://127.0.0.1:48082
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/infra/ws/**
          filters:
            - RewritePath=/infra/ws/v3/api-docs, /v3/api-docs
        - id: websocket # 路由的编号（WebSocket）
          #uri: grayLb://infra-server
          uri: ws://127.0.0.1:48082
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/infra/websocket/**
        - id: business-admin-api # 路由的编号
          #uri: grayLb://system-server
          uri: http://127.0.0.1:48090
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/business/**
          filters:
            - RewritePath=/admin-api/business/v3/api-docs, /v3/api-docs
        - id: sub-quartz-api # 路由的编号
          #uri: grayLb://system-server
          uri: http://127.0.0.1:48090
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/quartz/**
        - id: MHBus
          uri: http://127.0.0.1:48099
          predicates:
            - Path=/admin-api/MHBus/**
        - id: OutSys
          uri: http://127.0.0.1:48090
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/auth2/**
          filters:
            - RewritePath=/auth2/sysauthbytoken, /admin-api/auth2/sysauthbytoken # 配置，保证转发到 /v3/api-docs
      x-forwarded:
        prefix-enabled: false # 避免 Swagger 重复带上额外的 /admin-api/system 前缀

knife4j:
  # 聚合 Swagger 文档，参考 https://doc.xiaominfo.com/docs/action/springcloud-gateway 文档
  gateway:
    enabled: true
    routes:
      - name: system-server
        service-name: system-server
        url: /admin-api/system/v3/api-docs
      - name: infra-server
        service-name: infra-server
        url: /admin-api/infra/v3/api-docs
      - name: business-server
        service-name: business-server
        url: /admin-api/business/v3/api-docs

--- #################### luohang相关配置 ####################

microservices:
  info:
    version: 1.0.0
spring:
  main:
    allow-circular-references: true
server:
  tongweb:
    license:
      path=classpath: license.dat
com:
  tongtech:
    dino:
      client:
        enabled: true
        url: http://localhost:8083/dino
        username: admin
        password: <EMAIL>
remote-auth-log:
  file:
    request-url:
      - /admin-api/auth
      - /infra/ws/httpSend
