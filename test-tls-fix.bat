@echo off
echo ========================================
echo SQL Server TLS 兼容性修复测试
echo ========================================
echo.

cd microservices-module-system\microservices-module-system-biz

echo [1/4] 设置环境变量（启用TLS 1.0支持）...
set SPRING_PROFILES_ACTIVE=testlocal
set MAVEN_OPTS=-Xmx512m -XX:MaxMetaspaceSize=256m -Dhttps.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3 -Djdk.tls.client.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3 -Djdk.tls.disabledAlgorithms= -Djdk.certpath.disabledAlgorithms= -Dcom.sun.net.ssl.checkRevocation=false -Dcom.microsoft.sqlserver.jdbc.fips=false -Dcom.microsoft.sqlserver.jdbc.trustServerCertificate=true -Dcom.microsoft.sqlserver.jdbc.encrypt=false

echo [2/4] 清理并编译项目...
call mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo [3/4] 运行TLS连接测试...
call mvn exec:java -Dexec.mainClass="mh.cloud.module.system.util.TLSConnectionTest" -Dexec.classpathScope=compile -q
if %ERRORLEVEL% neq 0 (
    echo TLS测试失败！
    echo.
)

echo [4/4] 运行应用程序测试（可选）...
echo 注意：这将启动完整的Spring Boot应用程序
echo 如果不需要完整测试，请按Ctrl+C取消
pause

call mvn spring-boot:run -Dspring-boot.run.profiles=testlocal
if %ERRORLEVEL% neq 0 (
    echo 应用程序启动失败！
    echo 请检查数据库连接配置和TLS设置
)

echo.
echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 修复说明:
echo 1. 已更新SQL Server JDBC连接字符串模板
echo 2. 已配置JVM支持TLS 1.0-1.3协议
echo 3. 已禁用SSL证书验证（仅用于测试）
echo 4. 已添加SQL Server特定的SSL配置
echo.
echo 如果仍然遇到TLS错误，请检查：
echo - SQL Server版本和TLS配置
echo - 网络防火墙设置
echo - JDBC驱动版本兼容性
echo.
pause
