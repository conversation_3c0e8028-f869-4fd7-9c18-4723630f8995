@echo off
echo ========================================
echo SQLHelper 测试脚本
echo ========================================
echo.

cd microservices-module-system\microservices-module-system-biz

echo [1/4] 设置环境变量...
set SPRING_PROFILES_ACTIVE=testlocal
set MAVEN_OPTS=-Xmx512m -XX:MaxMetaspaceSize=256m -Dhttps.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3 -Djdk.tls.client.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3 -Dcom.sun.net.ssl.checkRevocation=false

echo [2/4] 清理并编译项目...
call mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo [3/5] 运行离线测试（完全不依赖外部服务）...
call mvn exec:java -Dexec.mainClass="mh.cloud.module.system.util.SQLHelperOfflineTest" -Dexec.classpathScope=compile -q
if %ERRORLEVEL% neq 0 (
    echo 离线测试失败！
    pause
    exit /b 1
)

echo [4/5] 运行简单测试（不依赖Spring）...
call mvn exec:java -Dexec.mainClass="mh.cloud.module.system.util.SimpleTest" -Dexec.classpathScope=compile -q
if %ERRORLEVEL% neq 0 (
    echo 简单测试失败！
    echo.
)

echo [5/5] 运行加密工具测试（包含SSL配置）...
call mvn exec:java -Dexec.mainClass="mh.cloud.module.system.util.SymmetricEncryptionUtils" -Dexec.classpathScope=compile -q

echo.
echo ========================================
echo 测试完成！
echo ========================================
pause
